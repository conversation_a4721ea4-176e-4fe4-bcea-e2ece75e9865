<?php
require_once 'config.php';

echo "<h2>Database Migration Script</h2>\n";
echo "<p>Adding missing columns to accounts table...</p>\n";

try {
    // Check if game_type column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM accounts LIKE 'game_type'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE accounts ADD COLUMN game_type VARCHAR(50) DEFAULT 'Mobile Legend' AFTER account_code");
        echo "✅ Added game_type column<br>\n";
    } else {
        echo "ℹ️ game_type column already exists<br>\n";
    }
    
    // Check if rank_tier column exists
    $stmt = $pdo->query("SHOW COLUMNS FROM accounts LIKE 'rank_tier'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE accounts ADD COLUMN rank_tier VARCHAR(50) DEFAULT 'Legend' AFTER device_info");
        echo "✅ Added rank_tier column<br>\n";
    } else {
        echo "ℹ️ rank_tier column already exists<br>\n";
    }
    
    // Check if language_code column exists in telegram_users
    $stmt = $pdo->query("SHOW COLUMNS FROM telegram_users LIKE 'language_code'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE telegram_users ADD COLUMN language_code VARCHAR(10) DEFAULT 'en' AFTER last_name");
        echo "✅ Added language_code column to telegram_users<br>\n";
    } else {
        echo "ℹ️ language_code column already exists in telegram_users<br>\n";
    }
    
    // Update last_activity to updated_at for consistency
    $stmt = $pdo->query("SHOW COLUMNS FROM telegram_users LIKE 'updated_at'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE telegram_users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP AFTER created_at");
        echo "✅ Added updated_at column to telegram_users<br>\n";
    } else {
        echo "ℹ️ updated_at column already exists in telegram_users<br>\n";
    }
    
    // Check if purchases table has the correct structure
    $stmt = $pdo->query("SHOW COLUMNS FROM purchases LIKE 'purchase_date'");
    if ($stmt->rowCount() > 0) {
        echo "ℹ️ purchases table uses purchase_date column (correct)<br>\n";
    } else {
        echo "⚠️ purchases table structure may need review<br>\n";
    }
    
    echo "<br><strong>✅ Database migration completed successfully!</strong><br>\n";
    echo "<p>You can now use all admin features without column errors.</p>\n";
    echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>\n";
    
} catch (PDOException $e) {
    echo "<strong>❌ Migration failed:</strong> " . $e->getMessage() . "<br>\n";
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Database Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; }
        h2 { color: #333; }
        p { margin: 1rem 0; }
        a { color: #3b82f6; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
