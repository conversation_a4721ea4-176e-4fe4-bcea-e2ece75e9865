<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$message = '';

// Handle purchase actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['complete_purchase'])) {
        $purchase_id = (int)$_POST['purchase_id'];
        try {
            $stmt = $pdo->prepare("UPDATE purchases SET payment_status = 'completed' WHERE id = ?");
            $stmt->execute([$purchase_id]);
            $message = "✅ Purchase marked as completed.";
        } catch (PDOException $e) {
            $message = "❌ Error updating purchase: " . $e->getMessage();
        }
    }
}

// Get all purchases
try {
    $stmt = $pdo->query("
        SELECT p.*, a.account_code, a.title as account_title, t.first_name, t.username as telegram_username
        FROM purchases p
        LEFT JOIN accounts a ON p.account_id = a.id
        LEFT JOIN telegram_users t ON p.telegram_user_id = t.id
        ORDER BY p.purchase_date DESC
    ");
    $purchases = $stmt->fetchAll();
} catch (PDOException $e) {
    $message = "❌ Error loading purchases: " . $e->getMessage();
    $purchases = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Purchases - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --sidebar-bg: #1e293b;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border: #e5e7eb;
            --hover: #f3f4f6;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            transform: translateX(4px);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .stats-summary {
            display: flex;
            gap: 2rem;
            font-size: 0.875rem;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary);
        }
        
        .stat-label {
            color: var(--text-secondary);
        }
        
        /* Content */
        .content {
            padding: 2rem;
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        /* Purchases Table */
        .purchases-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            overflow: hidden;
        }
        
        .table-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border);
            background: var(--hover);
        }
        
        .table-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .purchases-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .purchases-table th,
        .purchases-table td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }
        
        .purchases-table th {
            background: var(--hover);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .purchases-table tr:hover {
            background: var(--hover);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
        
        .status-completed {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-failed {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }
        
        .btn-success {
            background: var(--success);
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .btn-primary {
            background: var(--primary);
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .stats-summary {
                justify-content: space-around;
            }
            
            .purchases-table {
                font-size: 0.875rem;
            }
            
            .purchases-table th,
            .purchases-table td {
                padding: 0.75rem 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link active">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Purchase Management</h1>
            <div class="stats-summary">
                <div class="stat-item">
                    <div class="stat-value"><?php echo count($purchases); ?></div>
                    <div class="stat-label">Total Orders</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value"><?php echo count(array_filter($purchases, fn($p) => $p['payment_status'] === 'completed')); ?></div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">$<?php echo number_format(array_sum(array_map(fn($p) => $p['payment_status'] === 'completed' ? $p['amount'] : 0, $purchases)), 2); ?></div>
                    <div class="stat-label">Revenue</div>
                </div>
            </div>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?php echo strpos($message, '✅') !== false ? 'alert-success' : 'alert-error'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="purchases-container">
                <div class="table-header">
                    <h2 class="table-title">Recent Purchases</h2>
                </div>
                
                <?php if (empty($purchases)): ?>
                    <div class="empty-state">
                        <i class="fas fa-shopping-cart"></i>
                        <h3>No purchases found</h3>
                        <p>Purchases will appear here when customers buy accounts through the Telegram bot.</p>
                    </div>
                <?php else: ?>
                    <table class="purchases-table">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Account</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($purchases as $purchase): ?>
                                <tr>
                                    <td>
                                        <strong>#<?php echo str_pad($purchase['id'], 4, '0', STR_PAD_LEFT); ?></strong>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($purchase['first_name'] ?? 'Unknown'); ?></strong>
                                            <?php if ($purchase['telegram_username']): ?>
                                                <br><small>@<?php echo htmlspecialchars($purchase['telegram_username']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($purchase['account_code'] ?? 'N/A'); ?></strong>
                                            <br><small><?php echo htmlspecialchars($purchase['account_title'] ?? 'Account'); ?></small>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>$<?php echo number_format($purchase['amount'], 2); ?></strong>
                                    </td>
                                    <td>
                                        <span class="status-badge status-<?php echo $purchase['payment_status']; ?>">
                                            <?php echo ucfirst($purchase['payment_status']); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php echo date('M j, Y H:i', strtotime($purchase['purchase_date'])); ?>
                                    </td>
                                    <td>
                                        <?php if ($purchase['payment_status'] === 'pending'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="purchase_id" value="<?php echo $purchase['id']; ?>">
                                                <button type="submit" name="complete_purchase" class="btn btn-success">
                                                    <i class="fas fa-check"></i> Complete
                                                </button>
                                            </form>
                                        <?php else: ?>
                                            <span class="btn btn-primary" style="opacity: 0.6; cursor: default;">
                                                <i class="fas fa-check-circle"></i> Completed
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
