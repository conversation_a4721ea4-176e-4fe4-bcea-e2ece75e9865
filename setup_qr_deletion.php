<?php
require_once 'config.php';

try {
    // Create QR deletion schedule table
    $sql = "
    CREATE TABLE IF NOT EXISTS qr_deletion_schedule (
        id INT AUTO_INCREMENT PRIMARY KEY,
        chat_id BIGINT NOT NULL,
        md5 VARCHAR(32) NOT NULL,
        delete_at DATETIME NOT NULL,
        processed TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_qr (chat_id, md5),
        INDEX idx_delete_at (delete_at),
        INDEX idx_processed (processed)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "✅ QR deletion schedule table created successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}
?>
