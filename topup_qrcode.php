<?php
require_once 'config.php';

// Get parameters
$transaction_id = $_GET['transaction_id'] ?? '';
$telegram_id = $_GET['telegram_id'] ?? '';

if (empty($transaction_id) || empty($telegram_id)) {
    die('Missing required parameters');
}

try {
    // Get the top-up request
    $stmt = $pdo->prepare("
        SELECT tr.*, tu.telegram_id, tu.first_name, tu.last_name 
        FROM topup_requests tr
        JOIN telegram_users tu ON tr.telegram_user_id = tu.id
        WHERE tr.transaction_id = ? AND tu.telegram_id = ?
    ");
    $stmt->execute([$transaction_id, $telegram_id]);
    $request = $stmt->fetch();
    
    if (!$request) {
        die('Top-up request not found');
    }
    
    // Check if expired
    if ($request['status'] === 'expired' || strtotime($request['expires_at']) < time()) {
        die('This top-up request has expired');
    }
    
    // Check if already completed
    if ($request['status'] === 'completed') {
        die('This top-up request has already been completed');
    }
    
    $amount = $request['amount'];
    $user_name = $request['first_name'] . ' ' . $request['last_name'];
    
} catch (PDOException $e) {
    die('Database error: ' . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Top-Up Payment - <?php echo htmlspecialchars($user_name); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://github.com/davidhuotkeo/bakong-khqr/releases/download/bakong-khqr-1.0.6/khqr-1.0.6.min.js"></script>
    <style>
        :root {
            --primary: #3b82f6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #1f2937;
            --light: #f8fafc;
            --border: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }

        .payment-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 100%;
            text-align: center;
        }

        .payment-header {
            margin-bottom: 2rem;
        }

        .payment-icon {
            width: 64px;
            height: 64px;
            background: var(--primary);
            color: white;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            margin: 0 auto 1rem;
        }

        .payment-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .payment-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .payment-details {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .detail-row:last-child {
            margin-bottom: 0;
        }

        .detail-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .detail-value {
            font-weight: 600;
            color: var(--text-primary);
        }

        .amount-value {
            font-size: 1.25rem;
            color: var(--success);
        }

        .qr-container {
            background: white;
            border: 2px solid var(--border);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 2rem;
            display: inline-block;
        }

        .qr-code {
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .timer-container {
            background: #fef3c7;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .timer-text {
            color: #92400e;
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .timer-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: #92400e;
        }

        .instructions {
            text-align: left;
            background: #dbeafe;
            border: 1px solid #93c5fd;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 2rem;
        }

        .instructions h4 {
            color: #1e40af;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .instructions ol {
            color: #1e40af;
            font-size: 0.875rem;
            padding-left: 1.25rem;
        }

        .instructions li {
            margin-bottom: 0.25rem;
        }

        .status-container {
            display: none;
            background: #d1fae5;
            border: 1px solid #a7f3d0;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .status-text {
            color: #065f46;
            font-weight: 500;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0.25rem;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
        }

        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }

        .btn-secondary:hover {
            background: #4b5563;
        }

        @media (max-width: 480px) {
            .payment-container {
                padding: 1.5rem;
            }
            
            .qr-code {
                width: 150px;
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="payment-header">
            <div class="payment-icon">
                <i class="fas fa-qrcode"></i>
            </div>
            <h1 class="payment-title">Top-Up Payment</h1>
            <p class="payment-subtitle">Scan the QR code to complete your payment</p>
        </div>

        <div class="payment-details">
            <div class="detail-row">
                <span class="detail-label">User:</span>
                <span class="detail-value"><?php echo htmlspecialchars($user_name); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Amount:</span>
                <span class="detail-value amount-value">$<?php echo number_format($amount, 2); ?></span>
            </div>
            <div class="detail-row">
                <span class="detail-label">Transaction ID:</span>
                <span class="detail-value" style="font-size: 0.75rem;"><?php echo htmlspecialchars($transaction_id); ?></span>
            </div>
        </div>

        <div class="timer-container">
            <div class="timer-text">Payment expires in:</div>
            <div class="timer-value" id="countdown">Loading...</div>
        </div>

        <div class="qr-container">
            <div id="qr-code" class="qr-code"></div>
        </div>

        <div class="instructions">
            <h4><i class="fas fa-info-circle"></i> Payment Instructions:</h4>
            <ol>
                <li>Open your banking app or digital wallet</li>
                <li>Scan the QR code above</li>
                <li>Confirm the payment amount: $<?php echo number_format($amount, 2); ?></li>
                <li>Complete the payment</li>
                <li>Your balance will be updated automatically</li>
            </ol>
        </div>

        <div class="status-container" id="status-container">
            <div class="status-text" id="status-text">
                <i class="fas fa-check-circle"></i>
                Payment completed successfully!
            </div>
        </div>

        <div style="text-align: center;">
            <button class="btn btn-primary" onclick="checkPaymentStatus()">
                <i class="fas fa-sync"></i>
                Check Status
            </button>
            <a href="#" class="btn btn-secondary" onclick="cancelPayment()">
                <i class="fas fa-times"></i>
                Cancel
            </a>
        </div>
    </div>

    <script>
        const transactionId = '<?php echo $transaction_id; ?>';
        const telegramId = '<?php echo $telegram_id; ?>';
        const expiresAt = new Date('<?php echo $request['expires_at']; ?>');
        
        // Initialize QR code
        document.addEventListener("DOMContentLoaded", function () {
            generateQRCode();
            startCountdown();
            
            // Check status every 10 seconds
            setInterval(checkPaymentStatus, 10000);
        });

        function generateQRCode() {
            const { BakongKHQR, khqrData } = window.BakongKHQR;

            const merchantInfo = {
                bakongAccountID: "chhunlichhean_kun@wing",
                merchantName: "ACCOUNT-SHOP",
                merchantCity: "Phnom Penh",
                merchantId: "1263629",
                acquiringBank: "Bakong",
            };

            const optionalData = {
                currency: khqrData.currency.usd,
                amount: <?php echo $amount; ?>,
                billNumber: transactionId,
            };

            try {
                const khqr = new BakongKHQR(merchantInfo, optionalData);
                const qrCodeDataURL = khqr.generateQRCode();
                
                const qrContainer = document.getElementById('qr-code');
                qrContainer.innerHTML = `<img src="${qrCodeDataURL}" alt="Payment QR Code" style="width: 100%; height: 100%;">`;
            } catch (error) {
                console.error('Error generating QR code:', error);
                document.getElementById('qr-code').innerHTML = '<p style="color: red;">Error generating QR code</p>';
            }
        }

        function startCountdown() {
            function updateCountdown() {
                const now = new Date().getTime();
                const expiry = expiresAt.getTime();
                const distance = expiry - now;

                if (distance < 0) {
                    document.getElementById('countdown').innerHTML = 'EXPIRED';
                    document.querySelector('.timer-container').style.background = '#fee2e2';
                    document.querySelector('.timer-container').style.borderColor = '#fecaca';
                    document.querySelector('.timer-value').style.color = '#991b1b';
                    return;
                }

                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                document.getElementById('countdown').innerHTML = 
                    String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
            }

            updateCountdown();
            setInterval(updateCountdown, 1000);
        }

        async function checkPaymentStatus() {
            try {
                const response = await fetch('topup_request.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        telegram_id: parseInt(telegramId),
                        amount: <?php echo $amount; ?>,
                        action: 'check_status',
                        transaction_id: transactionId
                    })
                });

                const result = await response.json();
                
                if (result.success && result.data.status === 'completed') {
                    document.getElementById('status-container').style.display = 'block';
                    document.querySelector('.timer-container').style.display = 'none';
                    document.querySelector('.qr-container').style.display = 'none';
                    document.querySelector('.instructions').style.display = 'none';
                }
            } catch (error) {
                console.error('Error checking payment status:', error);
            }
        }

        async function cancelPayment() {
            if (confirm('Are you sure you want to cancel this payment?')) {
                try {
                    const response = await fetch('topup_request.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            telegram_id: parseInt(telegramId),
                            amount: <?php echo $amount; ?>,
                            action: 'cancel_topup',
                            transaction_id: transactionId
                        })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        alert('Payment cancelled successfully');
                        window.close();
                    } else {
                        alert('Error cancelling payment: ' + result.message);
                    }
                } catch (error) {
                    console.error('Error cancelling payment:', error);
                    alert('Error cancelling payment');
                }
            }
        }
    </script>
</body>
</html>
