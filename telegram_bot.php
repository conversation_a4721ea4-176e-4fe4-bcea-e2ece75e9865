<?php
require_once 'config.php';

// Telegram Bot API functions
class TelegramBot {
    private $token;
    private $api_url;
    private $pdo;
    
    public function __construct($token, $pdo) {
        $this->token = $token;
        $this->api_url = "https://api.telegram.org/bot{$token}/";
        $this->pdo = $pdo;
    }
    
    public function sendMessage($chat_id, $text, $reply_markup = null) {
        $data = [
            'chat_id' => $chat_id,
            'text' => $text,
            'parse_mode' => 'HTML'
        ];
        
        if ($reply_markup) {
            $data['reply_markup'] = json_encode($reply_markup);
        }
        
        return $this->makeRequest('sendMessage', $data);
    }
    
    public function sendPhoto($chat_id, $photo, $caption = '', $reply_markup = null) {
        // If photo is a URL, try to upload the file directly
        if (filter_var($photo, FILTER_VALIDATE_URL)) {
            // Convert URL to local file path
            $url_path = parse_url($photo, PHP_URL_PATH);
            $local_path = __DIR__ . $url_path;

            if (file_exists($local_path)) {
                // Use cURL to upload file directly
                $api_url = TELEGRAM_API_URL . 'sendPhoto';

                $post_data = [
                    'chat_id' => $chat_id,
                    'photo' => new CURLFile($local_path),
                    'caption' => $caption,
                    'parse_mode' => 'HTML'
                ];

                if ($reply_markup) {
                    $post_data['reply_markup'] = json_encode($reply_markup);
                }

                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_POST, 1);
                curl_setopt($ch, CURLOPT_POSTFIELDS, $post_data);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: multipart/form-data'
                ]);

                $result = curl_exec($ch);
                curl_close($ch);

                return json_decode($result, true);
            }
        }

        // Fallback to URL method
        $data = [
            'chat_id' => $chat_id,
            'photo' => $photo,
            'caption' => $caption,
            'parse_mode' => 'HTML'
        ];

        if ($reply_markup) {
            $data['reply_markup'] = json_encode($reply_markup);
        }

        return $this->makeRequest('sendPhoto', $data);
    }
    
    public function editMessageText($chat_id, $message_id, $text, $reply_markup = null) {
        $data = [
            'chat_id' => $chat_id,
            'message_id' => $message_id,
            'text' => $text,
            'parse_mode' => 'HTML'
        ];
        
        if ($reply_markup) {
            $data['reply_markup'] = json_encode($reply_markup);
        }
        
        return $this->makeRequest('editMessageText', $data);
    }
    
    private function makeRequest($method, $data) {
        $url = $this->api_url . $method;
        
        $options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($data)
            ]
        ];
        
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        
        return json_decode($result, true);
    }
    
    public function getUser($telegram_id) {
        $stmt = $this->pdo->prepare("SELECT * FROM telegram_users WHERE telegram_id = ?");
        $stmt->execute([$telegram_id]);
        return $stmt->fetch();
    }
    
    public function createUser($telegram_id, $username, $first_name, $last_name) {
        $stmt = $this->pdo->prepare("
            INSERT INTO telegram_users (telegram_id, username, first_name, last_name) 
            VALUES (?, ?, ?, ?) 
            ON DUPLICATE KEY UPDATE 
            username = VALUES(username), 
            first_name = VALUES(first_name), 
            last_name = VALUES(last_name),
            last_activity = CURRENT_TIMESTAMP
        ");
        return $stmt->execute([$telegram_id, $username, $first_name, $last_name]);
    }
    
    public function logInteraction($telegram_user_id, $action, $details = '') {
        $stmt = $this->pdo->prepare("
            INSERT INTO bot_interactions (telegram_user_id, action, details) 
            VALUES (?, ?, ?)
        ");
        return $stmt->execute([$telegram_user_id, $action, $details]);
    }
    
    public function getAvailableAccounts($limit = 10, $offset = 0) {
        $stmt = $this->pdo->prepare("
            SELECT * FROM accounts 
            WHERE status = 'available' 
            ORDER BY created_at DESC 
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$limit, $offset]);
        return $stmt->fetchAll();
    }
    
    public function getAccount($id) {
        $stmt = $this->pdo->prepare("SELECT * FROM accounts WHERE id = ? AND status = 'available'");
        $stmt->execute([$id]);
        return $stmt->fetch();
    }
}

// This section moved to the bottom of the file

function handleMessage($bot, $message) {
    $chat_id = $message['chat']['id'];
    $text = $message['text'] ?? '';
    $user = $message['from'];

    // Ignore system messages (messages from the bot itself)
    if (isset($user['is_bot']) && $user['is_bot']) {
        error_log("DEBUG: Ignoring bot message: $text");
        return;
    }

    // Ignore system-generated messages (expiration, confirmation, etc.)
    if (strpos($text, '⏰ QR code expired') !== false ||
        strpos($text, '✅ Payment') !== false ||
        strpos($text, '💰 Balance updated') !== false) {
        error_log("DEBUG: Ignoring system message: $text");
        return;
    }

    // Check for pending payments on every message (auto-check like Python bot)
    checkPendingPayments($chat_id);

    // Auto-check for completed payments (more aggressive checking)
    autoCheckCompletedPayments($chat_id);

    // Check for expired QR codes that need to be deleted
    checkExpiredQRCodes($chat_id);

    // Create or update user
    $telegram_user = $bot->getUser($user['id']);
    if (!$telegram_user) {
        $bot->createUser(
            $user['id'],
            $user['username'] ?? '',
            $user['first_name'] ?? '',
            $user['last_name'] ?? ''
        );
        $telegram_user = $bot->getUser($user['id']);
    }

    // Log interaction
    $bot->logInteraction($telegram_user['id'], 'message', $text);
    
    // Handle commands
    switch ($text) {
        case '/start':
        case '/browse':
            $welcome_text = "🎮 <b>Welcome to Account Store!</b>\n\n";
            $welcome_text .= "Browse and purchase premium gaming accounts.\n";
            $welcome_text .= "All accounts are verified and ready to use!\n\n";
            $welcome_text .= "Use the buttons below to get started:";
            
            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '🛍️ Browse Accounts', 'callback_data' => 'browse_accounts'],
                        ['text' => '💰 My Purchases', 'callback_data' => 'my_purchases']
                    ],
                    [
                        ['text' => '💳 My Balance', 'callback_data' => 'check_balance'],
                        ['text' => '💸 Top Up', 'callback_data' => 'topup_menu']
                    ],
                    [
                        ['text' => 'ℹ️ Help', 'callback_data' => 'help'],
                        ['text' => '📞 Support', 'callback_data' => 'support']
                    ]
                ]
            ];
            
            $bot->sendMessage($chat_id, $welcome_text, $keyboard);
            break;
            
        case '/browse':
            showAccountsList($bot, $chat_id, 0);
            break;
            
        case '/help':
            showHelp($bot, $chat_id);
            break;

        case '/support':
            showSupport($bot, $chat_id);
            break;

        case '/orders':
            showMyPurchases($bot, $chat_id, $telegram_user['id']);
            break;

        case '/balance':
            showBalance($bot, $chat_id, $telegram_user['id']);
            break;

        case '/topup':
            startTopUpFlow($bot, $chat_id, $telegram_user['id']);
            break;

        default:
            // Add debugging
            error_log("DEBUG: Processing message: '$text' from user: " . $telegram_user['id']);

            // Check if user is in top-up flow waiting for amount
            if (isUserWaitingForTopUpAmount($telegram_user['id'])) {
                error_log("DEBUG: User is waiting for top-up amount");
                handleTopUpAmountInput($bot, $chat_id, $telegram_user['id'], $text);
            } elseif (preg_match('/^\/topup\s+(\d+(?:\.\d{1,2})?)$/', $text, $matches)) {
                // Direct amount top-up
                error_log("DEBUG: Direct top-up command detected");
                $amount = (float)$matches[1];
                processDirectTopUp($bot, $chat_id, $telegram_user['id'], $amount);
            } else {
                error_log("DEBUG: Unknown command: '$text'");

                // Clear any old states if user sends a command
                if (strpos($text, '/') === 0) {
                    clearUserTopUpState($telegram_user['id']);
                    error_log("DEBUG: Cleared old state due to new command: $text");
                }

                $bot->sendMessage($chat_id, "I don't understand that command. Use /start to see available options.");
            }
            break;
    }
}

function handleCallbackQuery($bot, $callback_query) {
    $chat_id = $callback_query['message']['chat']['id'];
    $message_id = $callback_query['message']['message_id'];
    $data = $callback_query['data'];
    $user = $callback_query['from'];
    
    // Get user
    $telegram_user = $bot->getUser($user['id']);
    if ($telegram_user) {
        $bot->logInteraction($telegram_user['id'], 'callback', $data);
    }
    
    // Parse callback data
    $parts = explode('_', $data);
    $action = $parts[0];
    
    switch ($action) {
        case 'browse':
            if ($parts[1] === 'accounts') {
                $page = isset($parts[2]) ? intval($parts[2]) : 0;
                showAccountsList($bot, $chat_id, $page, $message_id);
            }
            break;
            
        case 'view':
            if ($parts[1] === 'account') {
                $account_id = intval($parts[2]);
                showAccountDetails($bot, $chat_id, $account_id, $message_id);
            }
            break;
            
        case 'buy':
            if ($parts[1] === 'account') {
                $account_id = intval($parts[2]);
                initiatePurchase($bot, $chat_id, $account_id, $telegram_user['id'], $message_id);
            }
            break;
            
        case 'back':
            if ($parts[1] === 'accounts') {
                $page = isset($parts[2]) ? intval($parts[2]) : 0;
                showAccountsList($bot, $chat_id, $page, $message_id);
            }
            break;
            
        case 'my':
            if ($parts[1] === 'purchases') {
                showMyPurchases($bot, $chat_id, $telegram_user['id'], $message_id);
            }
            break;

        case 'check':
            if ($parts[1] === 'payment') {
                $md5 = $parts[2];
                handleManualPaymentCheck($bot, $chat_id, $md5);
            }
            break;
            
        case 'help':
            showHelp($bot, $chat_id, $message_id);
            break;
            
        case 'support':
            showSupport($bot, $chat_id, $message_id);
            break;

        case 'topup':
            if (is_numeric($parts[1])) {
                // Clear any existing state
                clearUserTopUpState($telegram_user['id']);
                $amount = (float)$parts[1];
                processDirectTopUp($bot, $chat_id, $telegram_user['id'], $amount);
            }
            break;

        case 'cancel':
            if ($parts[1] === 'topup') {
                clearUserTopUpState($telegram_user['id']);
                $bot->sendMessage($chat_id, "❌ Top-up cancelled.");
            } elseif ($parts[1] === 'payment') {
                $md5 = $parts[2] ?? '';
                cancelPayment($bot, $chat_id, $telegram_user['id'], $md5);
            }
            break;

        case 'check':
            if ($parts[1] === 'balance') {
                showBalance($bot, $chat_id, $telegram_user['id']);
            } elseif ($parts[1] === 'payment') {
                $md5 = $parts[2] ?? '';
                checkPaymentStatus($bot, $chat_id, $telegram_user['id'], $md5);
            }
            break;

        case 'cancel':
            if ($parts[1] === 'topup') {
                $transaction_id = implode('_', array_slice($parts, 2));
                cancelTopUpRequest($bot, $chat_id, $telegram_user['id'], $transaction_id);
            }
            break;

        case 'back':
            if ($parts[1] === 'to' && $parts[2] === 'menu') {
                // Send the main menu
                $welcome_text = "🎮 <b>Welcome to Account Store!</b>\n\n";
                $welcome_text .= "Browse and purchase premium gaming accounts.\n";
                $welcome_text .= "All accounts are verified and ready to use!\n\n";
                $welcome_text .= "Use the buttons below to get started:";

                $keyboard = [
                    'inline_keyboard' => [
                        [
                            ['text' => '🛍️ Browse Accounts', 'callback_data' => 'browse_accounts'],
                            ['text' => '💰 My Purchases', 'callback_data' => 'my_purchases']
                        ],
                        [
                            ['text' => '💳 My Balance', 'callback_data' => 'check_balance'],
                            ['text' => '💸 Top Up', 'callback_data' => 'start_topup']
                        ],
                        [
                            ['text' => 'ℹ️ Help', 'callback_data' => 'help'],
                            ['text' => '📞 Support', 'callback_data' => 'support']
                        ]
                    ]
                ];

                $bot->editMessageText($chat_id, $message_id, $welcome_text, $keyboard);
            }
            break;

        case 'transaction':
            if ($parts[1] === 'history') {
                showTransactionHistory($bot, $chat_id, $telegram_user['id']);
            }
            break;

        case 'start':
            if ($parts[1] === 'topup') {
                startTopUpFlow($bot, $chat_id, $telegram_user['id']);
            }
            break;
    }
}

function showAccountsList($bot, $chat_id, $page = 0, $message_id = null) {
    $limit = 5;
    $offset = $page * $limit;
    
    $accounts = $bot->getAvailableAccounts($limit, $offset);
    
    if (empty($accounts)) {
        $text = "😔 No accounts available at the moment.\nPlease check back later!";
        $keyboard = [
            'inline_keyboard' => [
                [['text' => '🔄 Refresh', 'callback_data' => 'browse_accounts']]
            ]
        ];
    } else {
        $text = "🛍️ <b>Available Accounts</b>\n\n";
        
        $keyboard_buttons = [];
        foreach ($accounts as $account) {
            $text .= "🎮 <b>" . htmlspecialchars($account['title']) . "</b>\n";

            // Show account code if available
            if ($account['account_code']) {
                $text .= "🆔 Code: " . htmlspecialchars($account['account_code']) . "\n";
            }

            $text .= "💰 Price: " . number_format($account['price'], 0) . "$\n";
            $text .= "✅ Status: [ " . htmlspecialchars($account['account_status']) . " ]\n";
            $text .= "\n";

            $keyboard_buttons[] = [
                ['text' => "👀 View Details", 'callback_data' => 'view_account_' . $account['id']]
            ];
        }
        
        // Navigation buttons
        $nav_buttons = [];
        if ($page > 0) {
            $nav_buttons[] = ['text' => '⬅️ Previous', 'callback_data' => 'browse_accounts_' . ($page - 1)];
        }
        if (count($accounts) === $limit) {
            $nav_buttons[] = ['text' => 'Next ➡️', 'callback_data' => 'browse_accounts_' . ($page + 1)];
        }
        
        if (!empty($nav_buttons)) {
            $keyboard_buttons[] = $nav_buttons;
        }
        
        $keyboard = ['inline_keyboard' => $keyboard_buttons];
    }
    
    if ($message_id) {
        $bot->editMessageText($chat_id, $message_id, $text, $keyboard);
    } else {
        $bot->sendMessage($chat_id, $text, $keyboard);
    }
}

function showAccountDetails($bot, $chat_id, $account_id, $message_id = null) {
    $account = $bot->getAccount($account_id);

    if (!$account) {
        $text = "❌ Account not found or no longer available.";
        $keyboard = [
            'inline_keyboard' => [
                [['text' => '⬅️ Back to Accounts', 'callback_data' => 'browse_accounts']]
            ]
        ];
    } else {
        // Format like the image you showed
        $text = "🎮 <b>" . htmlspecialchars($account['title']) . "</b>\n\n";

        // Account Code
        if ($account['account_code']) {
            $text .= "🆔 <b>Account Code:</b> " . htmlspecialchars($account['account_code']) . "\n\n";
        }

        $text .= "<b>Information:</b>\n\n";

        // Status information with brackets
        $text .= "✅ Account Status [ " . htmlspecialchars($account['account_status']) . " ]\n";
        $text .= "🔐 Verify code [ " . htmlspecialchars($account['verify_code']) . " ]\n";
        $text .= "⏰ Inactive [ " . htmlspecialchars($account['inactive_days']) . " ]\n";
        $text .= "🏆 Collector [ " . htmlspecialchars($account['collector_status']) . " ]\n";
        $text .= "📱 Device [ " . htmlspecialchars($account['device_info']) . " ]\n\n";

        // Price with emoji
        $text .= "💰 <b>Price:</b> " . number_format($account['price'], 0) . "$\n\n";

        // Additional info if available
        if ($account['additional_info']) {
            $text .= "📝 <b>Additional Info:</b>\n" . htmlspecialchars($account['additional_info']) . "\n\n";
        }

        $text .= "Click button below to buy account!";

        $keyboard = [
            'inline_keyboard' => [
                [['text' => '💳 Buy Now - $' . number_format($account['price'], 0), 'callback_data' => 'buy_account_' . $account['id']]],
                [['text' => '⬅️ Back to Accounts', 'callback_data' => 'browse_accounts']]
            ]
        ];

        // Send photo if available
        if ($account['image_path']) {
            $photo_url = SITE_URL . '/uploads/' . $account['image_path'];

            if ($message_id) {
                // For edited messages, send photo as new message then edit original
                $bot->sendPhoto($chat_id, $photo_url, $text, $keyboard);
                return;
            } else {
                // For new messages, send photo with caption
                $bot->sendPhoto($chat_id, $photo_url, $text, $keyboard);
                return;
            }
        }
    }

    if ($message_id) {
        $bot->editMessageText($chat_id, $message_id, $text, $keyboard);
    } else {
        $bot->sendMessage($chat_id, $text, $keyboard);
    }
}

function initiatePurchase($bot, $chat_id, $account_id, $telegram_user_id, $message_id = null) {
    $account = $bot->getAccount($account_id);

    if (!$account) {
        $text = "❌ Account not found or no longer available.";
        $keyboard = [
            'inline_keyboard' => [
                [['text' => '⬅️ Back to Accounts', 'callback_data' => 'browse_accounts']]
            ]
        ];
    } else {
        // Create purchase record
        try {
            $stmt = $bot->pdo->prepare("
                INSERT INTO purchases (account_id, telegram_user_id, amount, payment_status)
                VALUES (?, ?, ?, 'pending')
            ");
            $stmt->execute([$account_id, $telegram_user_id, $account['price']]);
            $purchase_id = $bot->pdo->lastInsertId();

            $text = "🛒 <b>Purchase Order Created!</b>\n\n";
            $text .= "🎮 <b>Account:</b> " . htmlspecialchars($account['title']) . "\n";

            if ($account['account_code']) {
                $text .= "🆔 <b>Code:</b> " . htmlspecialchars($account['account_code']) . "\n";
            }

            $text .= "💰 <b>Price:</b> " . number_format($account['price'], 0) . "$\n";
            $text .= "🆔 <b>Order ID:</b> <code>PUR-" . $purchase_id . "</code>\n\n";

            $text .= "🔄 <b>Status:</b> Waiting for Payment\n\n";
            $text .= "💳 <b>Payment Methods:</b>\n";
            $text .= "• PayPal\n";
            $text .= "• Crypto (USDT/BTC)\n";
            $text .= "• Bank Transfer\n\n";
            $text .= "📞 Contact support with your Order ID to complete payment.\n";
            $text .= "Account details will be delivered automatically after payment confirmation.";

            $keyboard = [
                'inline_keyboard' => [
                    [['text' => '💬 Contact Support', 'callback_data' => 'support']],
                    [['text' => '📦 My Orders', 'callback_data' => 'my_purchases']],
                    [['text' => '⬅️ Back to Browse', 'callback_data' => 'browse_accounts']]
                ]
            ];

        } catch (PDOException $e) {
            $text = "❌ Error creating purchase order. Please try again.";
            $keyboard = [
                'inline_keyboard' => [
                    [['text' => '⬅️ Back to Account', 'callback_data' => 'view_account_' . $account_id]]
                ]
            ];
        }
    }

    if ($message_id) {
        $bot->editMessageText($chat_id, $message_id, $text, $keyboard);
    } else {
        $bot->sendMessage($chat_id, $text, $keyboard);
    }
}

function showMyPurchases($bot, $chat_id, $telegram_user_id, $message_id = null) {
    $stmt = $bot->pdo->prepare("
        SELECT p.*, a.title 
        FROM purchases p 
        JOIN accounts a ON p.account_id = a.id 
        WHERE p.telegram_user_id = ? 
        ORDER BY p.purchase_date DESC 
        LIMIT 10
    ");
    $stmt->execute([$telegram_user_id]);
    $purchases = $stmt->fetchAll();
    
    if (empty($purchases)) {
        $text = "📦 <b>My Purchases</b>\n\n";
        $text .= "You haven't made any purchases yet.\n";
        $text .= "Browse our available accounts to get started!";
        
        $keyboard = [
            'inline_keyboard' => [
                [['text' => '🛍️ Browse Accounts', 'callback_data' => 'browse_accounts']]
            ]
        ];
    } else {
        $text = "📦 <b>My Purchases</b>\n\n";
        
        foreach ($purchases as $purchase) {
            $text .= "💎 <b>" . htmlspecialchars($purchase['title']) . "</b>\n";
            $text .= "💰 $" . number_format($purchase['amount'], 2) . "\n";
            $text .= "📅 " . date('M j, Y', strtotime($purchase['purchase_date'])) . "\n";
            $text .= "🔄 Status: " . ucfirst($purchase['payment_status']) . "\n";
            if ($purchase['payment_status'] === 'completed') {
                $text .= "✅ Account delivered\n";
            }
            $text .= "\n";
        }
        
        $keyboard = [
            'inline_keyboard' => [
                [['text' => '🛍️ Browse More Accounts', 'callback_data' => 'browse_accounts']]
            ]
        ];
    }
    
    if ($message_id) {
        $bot->editMessageText($chat_id, $message_id, $text, $keyboard);
    } else {
        $bot->sendMessage($chat_id, $text, $keyboard);
    }
}

function showHelp($bot, $chat_id, $message_id = null) {
    $text = "ℹ️ <b>Help & Information</b>\n\n";
    $text .= "🛍️ <b>How to buy:</b>\n";
    $text .= "1. Browse available accounts\n";
    $text .= "2. View account details\n";
    $text .= "3. Click 'Buy Now'\n";
    $text .= "4. Contact support for payment\n";
    $text .= "5. Receive account details\n\n";
    $text .= "💳 <b>Payment:</b> Contact support\n";
    $text .= "🔒 <b>Security:</b> All accounts verified\n";
    $text .= "📞 <b>Support:</b> Available 24/7\n\n";
    $text .= "❓ <b>Questions?</b> Contact our support team!";
    
    $keyboard = [
        'inline_keyboard' => [
            [['text' => '📞 Contact Support', 'callback_data' => 'support']],
            [['text' => '🛍️ Browse Accounts', 'callback_data' => 'browse_accounts']]
        ]
    ];
    
    if ($message_id) {
        $bot->editMessageText($chat_id, $message_id, $text, $keyboard);
    } else {
        $bot->sendMessage($chat_id, $text, $keyboard);
    }
}

function showSupport($bot, $chat_id, $message_id = null) {
    $text = "📞 <b>Support & Contact</b>\n\n";
    $text .= "Need help? Our support team is here for you!\n\n";
    $text .= "💬 <b>Telegram:</b> @YourSupportUsername\n";
    $text .= "📧 <b>Email:</b> <EMAIL>\n";
    $text .= "⏰ <b>Hours:</b> 24/7 Available\n\n";
    $text .= "🔄 <b>Response time:</b> Usually within 1 hour\n\n";
    $text .= "When contacting support, please include:\n";
    $text .= "• Your Telegram username\n";
    $text .= "• Purchase reference ID (if applicable)\n";
    $text .= "• Detailed description of your issue";
    
    $keyboard = [
        'inline_keyboard' => [
            [['text' => '⬅️ Back to Menu', 'callback_data' => 'browse_accounts']]
        ]
    ];
    
    if ($message_id) {
        $bot->editMessageText($chat_id, $message_id, $text, $keyboard);
    } else {
        $bot->sendMessage($chat_id, $text, $keyboard);
    }
}

function showBalance($bot, $chat_id, $telegram_user_id) {
    global $pdo;

    try {
        // Get user balance and transaction history
        $stmt = $pdo->prepare("
            SELECT tu.balance, tu.first_name, tu.last_name,
                   COUNT(bt.id) as transaction_count,
                   COALESCE(SUM(CASE WHEN bt.transaction_type = 'topup' THEN bt.amount ELSE 0 END), 0) as total_topups,
                   COALESCE(SUM(CASE WHEN bt.transaction_type = 'purchase' THEN ABS(bt.amount) ELSE 0 END), 0) as total_spent
            FROM telegram_users tu
            LEFT JOIN balance_transactions bt ON tu.id = bt.telegram_user_id
            WHERE tu.id = ?
            GROUP BY tu.id
        ");
        $stmt->execute([$telegram_user_id]);
        $user_data = $stmt->fetch();

        if (!$user_data) {
            $bot->sendMessage($chat_id, "❌ Error retrieving balance information.");
            return;
        }

        $text = "💰 <b>Your Balance</b>\n\n";
        $text .= "👤 <b>Name:</b> " . htmlspecialchars($user_data['first_name'] . ' ' . $user_data['last_name']) . "\n";
        $text .= "💵 <b>Current Balance:</b> $" . number_format($user_data['balance'], 2) . "\n\n";
        $text .= "📊 <b>Statistics:</b>\n";
        $text .= "• Total Top-ups: $" . number_format($user_data['total_topups'], 2) . "\n";
        $text .= "• Total Spent: $" . number_format($user_data['total_spent'], 2) . "\n";
        $text .= "• Transactions: " . $user_data['transaction_count'] . "\n\n";
        $text .= "💳 Use /topup to add funds to your balance";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '💳 Top Up Balance', 'callback_data' => 'topup_menu'],
                    ['text' => '📊 Transaction History', 'callback_data' => 'transaction_history']
                ],
                [
                    ['text' => '🔙 Back to Menu', 'callback_data' => 'back_to_menu']
                ]
            ]
        ];

        $bot->sendMessage($chat_id, $text, $keyboard);

    } catch (Exception $e) {
        error_log("Balance error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error retrieving balance. Please try again later.");
    }
}

function startTopUpFlow($bot, $chat_id, $telegram_user_id) {
    global $pdo;

    try {
        // Get top-up settings
        $stmt = $pdo->query("SELECT setting_name, setting_value FROM topup_settings");
        $settings_data = $stmt->fetchAll();

        $settings = [];
        foreach ($settings_data as $setting) {
            $settings[$setting['setting_name']] = $setting['setting_value'];
        }

        // Check if top-up is enabled
        if (($settings['topup_enabled'] ?? '1') !== '1') {
            $bot->sendMessage($chat_id, "❌ Top-up system is currently disabled. Please try again later.");
            return;
        }

        $min_amount = (float)($settings['min_topup_amount'] ?? 10);
        $max_amount = (float)($settings['max_topup_amount'] ?? 1000);

        // Set user state to waiting for amount
        setUserTopUpState($telegram_user_id, 'waiting_amount');

        $text = "💳 <b>Top-Up Your Balance</b>\n\n";
        $text .= "💰 <b>Limits:</b>\n";
        $text .= "• Minimum: $" . number_format($min_amount, 2) . "\n";
        $text .= "• Maximum: $" . number_format($max_amount, 2) . "\n\n";
        $text .= "💵 <b>Please enter the amount you want to top-up:</b>\n";
        $text .= "Example: 25.00\n\n";
        $text .= "Or choose a quick amount:";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '$10', 'callback_data' => 'topup_10'],
                    ['text' => '$25', 'callback_data' => 'topup_25'],
                    ['text' => '$50', 'callback_data' => 'topup_50']
                ],
                [
                    ['text' => '$100', 'callback_data' => 'topup_100'],
                    ['text' => '$200', 'callback_data' => 'topup_200'],
                    ['text' => '$500', 'callback_data' => 'topup_500']
                ],
                [
                    ['text' => '❌ Cancel', 'callback_data' => 'cancel_topup']
                ]
            ]
        ];

        $bot->sendMessage($chat_id, $text, $keyboard);

    } catch (Exception $e) {
        error_log("Top-up flow error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error starting top-up. Please try again later.");
    }
}

function processTopUpRequest($bot, $chat_id, $telegram_user_id, $amount) {
    global $pdo;

    try {
        // Get user telegram_id
        $stmt = $pdo->prepare("SELECT telegram_id FROM telegram_users WHERE id = ?");
        $stmt->execute([$telegram_user_id]);
        $user = $stmt->fetch();

        if (!$user) {
            $bot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        $telegram_id = $user['telegram_id'];

        // Create top-up request via API
        $request_data = [
            'telegram_id' => $telegram_id,
            'amount' => $amount,
            'action' => 'create_topup'
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, SITE_URL . '/topup_request.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code !== 200 || !$response) {
            $bot->sendMessage($chat_id, "❌ Error creating top-up request. Please try again later.");
            return;
        }

        $result = json_decode($response, true);

        if (!$result['success']) {
            $bot->sendMessage($chat_id, "❌ " . $result['message']);
            return;
        }

        $transaction_id = $result['data']['transaction_id'];
        $expiry_minutes = $result['data']['expiry_minutes'];

        $text = "✅ <b>Top-Up Request Created!</b>\n\n";
        $text .= "💰 <b>Amount:</b> $" . number_format($amount, 2) . "\n";
        $text .= "🆔 <b>Transaction ID:</b> <code>" . $transaction_id . "</code>\n";
        $text .= "⏰ <b>Expires in:</b> " . $expiry_minutes . " minutes\n\n";
        $text .= "👆 Click the button below to open the payment page and scan the QR code.";

        $payment_url = SITE_URL . "/topup_qrcode.php?transaction_id=" . urlencode($transaction_id) . "&telegram_id=" . $telegram_id;

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '💳 Pay Now', 'url' => $payment_url]
                ],
                [
                    ['text' => '🔍 Check Status', 'callback_data' => 'check_topup_' . $transaction_id],
                    ['text' => '❌ Cancel', 'callback_data' => 'cancel_topup_' . $transaction_id]
                ]
            ]
        ];

        $bot->sendMessage($chat_id, $text, $keyboard);

    } catch (Exception $e) {
        error_log("Top-up request error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error processing top-up request. Please try again later.");
    }
}

function checkTopUpStatus($bot, $chat_id, $telegram_user_id, $transaction_id) {
    global $pdo;

    try {
        // Get user telegram_id
        $stmt = $pdo->prepare("SELECT telegram_id FROM telegram_users WHERE id = ?");
        $stmt->execute([$telegram_user_id]);
        $user = $stmt->fetch();

        if (!$user) {
            $bot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        $telegram_id = $user['telegram_id'];

        // Check status via API
        $request_data = [
            'telegram_id' => $telegram_id,
            'amount' => 0, // Not needed for status check
            'action' => 'check_status',
            'transaction_id' => $transaction_id
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, SITE_URL . '/topup_request.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code !== 200 || !$response) {
            $bot->sendMessage($chat_id, "❌ Error checking status. Please try again later.");
            return;
        }

        $result = json_decode($response, true);

        if (!$result['success']) {
            $bot->sendMessage($chat_id, "❌ " . $result['message']);
            return;
        }

        $status = $result['data']['status'];
        $amount = $result['data']['amount'];

        $status_icons = [
            'pending' => '⏳',
            'completed' => '✅',
            'failed' => '❌',
            'expired' => '⏰'
        ];

        $icon = $status_icons[$status] ?? '❓';

        $text = "$icon <b>Top-Up Status</b>\n\n";
        $text .= "🆔 <b>Transaction ID:</b> <code>$transaction_id</code>\n";
        $text .= "💰 <b>Amount:</b> $" . number_format($amount, 2) . "\n";
        $text .= "📊 <b>Status:</b> " . ucfirst($status) . "\n\n";

        if ($status === 'completed') {
            $text .= "🎉 Payment completed successfully!\nYour balance has been updated.";
        } elseif ($status === 'pending') {
            $text .= "⏳ Payment is still pending.\nPlease complete the payment or wait for confirmation.";
        } elseif ($status === 'expired') {
            $text .= "⏰ This payment request has expired.\nPlease create a new top-up request.";
        } elseif ($status === 'failed') {
            $text .= "❌ Payment was cancelled or failed.\nYou can create a new top-up request.";
        }

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '💰 Check Balance', 'callback_data' => 'check_balance'],
                    ['text' => '💳 New Top-Up', 'callback_data' => 'topup_menu']
                ]
            ]
        ];

        $bot->sendMessage($chat_id, $text, $keyboard);

    } catch (Exception $e) {
        error_log("Check top-up status error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error checking status. Please try again later.");
    }
}

function cancelTopUpRequest($bot, $chat_id, $telegram_user_id, $transaction_id) {
    global $pdo;

    try {
        // Get user telegram_id
        $stmt = $pdo->prepare("SELECT telegram_id FROM telegram_users WHERE id = ?");
        $stmt->execute([$telegram_user_id]);
        $user = $stmt->fetch();

        if (!$user) {
            $bot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        $telegram_id = $user['telegram_id'];

        // Cancel via API
        $request_data = [
            'telegram_id' => $telegram_id,
            'amount' => 0, // Not needed for cancellation
            'action' => 'cancel_topup',
            'transaction_id' => $transaction_id
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, SITE_URL . '/topup_request.php');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        if ($http_code !== 200 || !$response) {
            $bot->sendMessage($chat_id, "❌ Error cancelling request. Please try again later.");
            return;
        }

        $result = json_decode($response, true);

        if ($result['success']) {
            $bot->sendMessage($chat_id, "✅ Top-up request cancelled successfully.");
        } else {
            $bot->sendMessage($chat_id, "❌ " . $result['message']);
        }

    } catch (Exception $e) {
        error_log("Cancel top-up error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error cancelling request. Please try again later.");
    }
}

function showTransactionHistory($bot, $chat_id, $telegram_user_id) {
    global $pdo;

    try {
        // Get recent transactions
        $stmt = $pdo->prepare("
            SELECT
                transaction_type,
                amount,
                balance_after,
                description,
                created_at
            FROM balance_transactions
            WHERE telegram_user_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        ");
        $stmt->execute([$telegram_user_id]);
        $transactions = $stmt->fetchAll();

        if (empty($transactions)) {
            $text = "📊 <b>Transaction History</b>\n\n";
            $text .= "No transactions found.\n";
            $text .= "Start by topping up your balance!";

            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '💳 Top Up Now', 'callback_data' => 'topup_menu'],
                        ['text' => '🔙 Back', 'callback_data' => 'check_balance']
                    ]
                ]
            ];
        } else {
            $text = "📊 <b>Transaction History</b>\n\n";
            $text .= "Recent transactions (last 10):\n\n";

            foreach ($transactions as $transaction) {
                $type_icons = [
                    'topup' => '💳',
                    'purchase' => '🛒',
                    'refund' => '↩️',
                    'admin_adjustment' => '⚙️'
                ];

                $icon = $type_icons[$transaction['transaction_type']] ?? '💰';
                $amount_color = $transaction['amount'] >= 0 ? '+' : '';

                $text .= "$icon <b>" . ucfirst(str_replace('_', ' ', $transaction['transaction_type'])) . "</b>\n";
                $text .= "💰 Amount: {$amount_color}$" . number_format($transaction['amount'], 2) . "\n";
                $text .= "💵 Balance: $" . number_format($transaction['balance_after'], 2) . "\n";
                if ($transaction['description']) {
                    $text .= "📝 " . htmlspecialchars($transaction['description']) . "\n";
                }
                $text .= "📅 " . date('M j, Y H:i', strtotime($transaction['created_at'])) . "\n\n";
            }

            $keyboard = [
                'inline_keyboard' => [
                    [
                        ['text' => '💰 Check Balance', 'callback_data' => 'check_balance'],
                        ['text' => '🔙 Back', 'callback_data' => 'back_to_menu']
                    ]
                ]
            ];
        }

        $bot->sendMessage($chat_id, $text, $keyboard);

    } catch (Exception $e) {
        error_log("Transaction history error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error loading transaction history. Please try again later.");
    }
}

// User state management functions
function setUserTopUpState($telegram_user_id, $state) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO bot_interactions (telegram_user_id, action, details)
            VALUES (?, 'topup_state', ?)
            ON DUPLICATE KEY UPDATE details = VALUES(details), created_at = CURRENT_TIMESTAMP
        ");
        $stmt->execute([$telegram_user_id, $state]);
    } catch (Exception $e) {
        error_log("Set user state error: " . $e->getMessage());
    }
}

function getUserTopUpState($telegram_user_id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT details FROM bot_interactions
            WHERE telegram_user_id = ? AND action = 'topup_state'
            AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$telegram_user_id]);
        $result = $stmt->fetch();

        return $result ? $result['details'] : null;
    } catch (Exception $e) {
        error_log("Get user state error: " . $e->getMessage());
        return null;
    }
}

function clearUserTopUpState($telegram_user_id) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            DELETE FROM bot_interactions
            WHERE telegram_user_id = ? AND action = 'topup_state'
        ");
        $stmt->execute([$telegram_user_id]);
    } catch (Exception $e) {
        error_log("Clear user state error: " . $e->getMessage());
    }
}

function isUserWaitingForTopUpAmount($telegram_user_id) {
    $state = getUserTopUpState($telegram_user_id);
    error_log("DEBUG: User $telegram_user_id state: '$state'");
    return $state === 'waiting_amount';
}

function handleTopUpAmountInput($bot, $chat_id, $telegram_user_id, $text) {
    // Add debugging
    error_log("DEBUG: handleTopUpAmountInput called with text: '$text' for user: $telegram_user_id");

    // Clear the state first
    clearUserTopUpState($telegram_user_id);

    // Validate amount input
    if (!is_numeric($text) || (float)$text <= 0) {
        error_log("DEBUG: Invalid amount input: '$text'");
        $bot->sendMessage($chat_id, "❌ Please enter a valid amount (numbers only).\nExample: 25.00\n\nUse /topup to try again.");
        return;
    }

    $amount = (float)$text;
    error_log("DEBUG: Processing amount: $amount");
    processDirectTopUp($bot, $chat_id, $telegram_user_id, $amount);
}

function processDirectTopUp($bot, $chat_id, $telegram_user_id, $amount) {
    global $pdo;

    error_log("DEBUG: processDirectTopUp called with amount: $amount for user: $telegram_user_id");

    try {
        // Get top-up settings
        $stmt = $pdo->query("SELECT setting_name, setting_value FROM topup_settings");
        $settings_data = $stmt->fetchAll();

        $settings = [];
        foreach ($settings_data as $setting) {
            $settings[$setting['setting_name']] = $setting['setting_value'];
        }

        $min_amount = (float)($settings['min_topup_amount'] ?? 10);
        $max_amount = (float)($settings['max_topup_amount'] ?? 1000);

        // Validate amount
        error_log("DEBUG: Validating amount $amount (min: $min_amount, max: $max_amount)");

        if ($amount < $min_amount) {
            error_log("DEBUG: Amount too small");
            $bot->sendMessage($chat_id, "❌ Minimum top-up amount is $" . number_format($min_amount, 2) . "\n\nUse /topup to try again.");
            return;
        }

        if ($amount > $max_amount) {
            error_log("DEBUG: Amount too large");
            $bot->sendMessage($chat_id, "❌ Maximum top-up amount is $" . number_format($max_amount, 2) . "\n\nUse /topup to try again.");
            return;
        }

        error_log("DEBUG: Amount validation passed");

        // Get user info
        error_log("DEBUG: Getting user info for user ID: $telegram_user_id");
        $stmt = $pdo->prepare("SELECT telegram_id, username, first_name, last_name FROM telegram_users WHERE id = ?");
        $stmt->execute([$telegram_user_id]);
        $user = $stmt->fetch();

        if (!$user) {
            error_log("DEBUG: User not found in database");
            $bot->sendMessage($chat_id, "❌ User not found.");
            return;
        }

        error_log("DEBUG: User found: " . json_encode($user));

        // Generate QR code and create payment record
        // Generate QR using KHQR API (like your Python example)
        $qr_result = generateQRViaAPI($user, $amount);

        if (!$qr_result['success']) {
            $bot->sendMessage($chat_id, "❌ " . $qr_result['message']);
            return;
        }

        // Send QR code to user
        sendQRCodeViaAPI($bot, $chat_id, $qr_result['data'], $amount);

        // Clear user state since QR was successfully generated
        clearUserTopUpState($telegram_user['id']);
        error_log("DEBUG: Cleared user state after QR generation for user: " . $telegram_user['id']);

        // Start automatic payment monitoring (like your Python auto_check_transaction)
        startAPIPaymentMonitoring($chat_id, $qr_result['data']['md5'], $amount);

        // Start auto payment checking (checks every 2 seconds automatically)
        startAutoPaymentChecking($chat_id, $qr_result['data']['md5'], $amount);

        // Start background process to delete QR after 3 minutes (like your Python threading)
        startBackgroundQRDeletion($chat_id, $qr_result['data']['md5']);



    } catch (Exception $e) {
        error_log("Process top-up error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error processing top-up. Please try again later.");
    }
}

// Handle manual payment check button
function handleManualPaymentCheck($bot, $chat_id, $md5) {
    global $pdo;

    try {
        // Get transaction details (check all statuses)
        $stmt = $pdo->prepare("SELECT amount, status FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        $transaction = $stmt->fetch();

        if ($transaction) {
            if ($transaction['status'] === 'completed') {
                $bot->sendMessage($chat_id, "✅ This payment was already processed successfully!");
                return;
            } elseif ($transaction['status'] === 'expired') {
                $bot->sendMessage($chat_id, "⏰ This QR code has expired. Please generate a new one.");
                return;
            } elseif ($transaction['status'] === 'pending') {
                $bot->sendMessage($chat_id, "🔍 Checking payment status...");

                // Check payment status
                if (checkPaymentStatusAPI($chat_id, $md5, $transaction['amount'])) {
                    // Payment found and handled by checkPaymentStatusAPI
                    return;
                } else {
                    $bot->sendMessage($chat_id, "⏳ Payment not detected yet. Please wait a moment after payment and try again.");
                }
            }
        } else {
            $bot->sendMessage($chat_id, "❌ Transaction not found.");
        }

    } catch (Exception $e) {
        error_log("Manual payment check error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error checking payment status.");
    }
}

// Generate QR using KHQR API (like your Python example)
function generateQRViaAPI($user, $amount) {
    error_log("DEBUG: Generating QR via API for amount: $amount");

    try {
        // API URL with your exact parameters (same as Python code)
        $api_url = "https://api.kunchhunlichhean.org/khqr/create?amount={$amount}&bakongid=chhunlichhean_kun@wing&merchantname=CHHEANSMM";
        error_log("DEBUG: API URL: $api_url");

        // Make GET request to the API
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        error_log("DEBUG: API Response - HTTP: $http_code, Response: $response");

        if ($http_code == 200 || $http_code == 201) {
            $response_data = json_decode($response, true);
            error_log("DEBUG: Parsed API response: " . json_encode($response_data));

            $qr_code_url = $response_data['qr'] ?? null;
            $md5 = $response_data['md5'] ?? null;

            if ($qr_code_url && $md5) {
                error_log("DEBUG: QR generation successful - QR URL: $qr_code_url, MD5: $md5");
                return [
                    'success' => true,
                    'data' => [
                        'qr_url' => $qr_code_url,
                        'md5' => $md5,
                        'amount' => $amount
                    ]
                ];
            } else {
                error_log("DEBUG: Missing QR URL or MD5 in response");
                return [
                    'success' => false,
                    'message' => 'Failed to generate QR code: Missing QR code URL or MD5 in response.'
                ];
            }
        } else {
            error_log("DEBUG: API request failed with HTTP code: $http_code");
            return [
                'success' => false,
                'message' => "Failed to make the request. Status code: {$http_code}"
            ];
        }

    } catch (Exception $e) {
        error_log("DEBUG: API exception: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'An error occurred: ' . $e->getMessage()
        ];
    }
}

// Send QR code to user (like your Python example)
function sendQRCodeViaAPI($bot, $chat_id, $qr_data, $amount) {
    try {
        $text = "💳 <b>QR Code for {$amount} USD</b>\n\n";
        $text .= "📱 <b>Scan with your banking app</b>\n";
        $text .= "⏰ <b>Expires in 3 minutes...</b>\n\n";
        $text .= "🔄 <b>Auto-checking payment every 2 seconds...</b>\n";
        $text .= "💳 <b>Pay now and wait for confirmation!</b>";

        // Add auto-checking message (no button needed)
        $keyboard = null; // Remove button since we'll auto-check

        // Send QR code image with check button
        $message_response = $bot->sendPhoto($chat_id, $qr_data['qr_url'], $text, $keyboard);

        // Extract message_id from response
        $message_id = null;
        if ($message_response && isset($message_response['result']['message_id'])) {
            $message_id = $message_response['result']['message_id'];
        }

        // Store transaction for monitoring
        storeTransaction($chat_id, $qr_data['md5'], $message_id, $amount);

    } catch (Exception $e) {
        error_log("Send QR via API error: " . $e->getMessage());
        $bot->sendMessage($chat_id, "❌ Error sending QR code. Please try again.");
    }
}

// Store transaction (like your Python transactions dict)
function storeTransaction($chat_id, $md5, $message_id, $amount) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO bot_transactions (chat_id, md5, message_id, amount, created_at, status)
            VALUES (?, ?, ?, ?, NOW(), 'pending')
            ON DUPLICATE KEY UPDATE
            md5 = VALUES(md5),
            message_id = VALUES(message_id),
            amount = VALUES(amount),
            created_at = VALUES(created_at),
            status = 'pending'
        ");
        $stmt->execute([$chat_id, $md5, $message_id, $amount]);

    } catch (Exception $e) {
        error_log("Store transaction error: " . $e->getMessage());
    }
}

// Start automatic payment monitoring (like your Python threading)
function startAPIPaymentMonitoring($chat_id, $md5, $amount) {
    // In PHP, we'll use a different approach since we don't have threading
    // We'll store the monitoring request and check it via cron or webhook

    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO payment_monitoring (chat_id, md5, amount, started_at, check_count, max_checks)
            VALUES (?, ?, ?, NOW(), 0, 18)
            ON DUPLICATE KEY UPDATE
            started_at = VALUES(started_at),
            check_count = 0,
            max_checks = 18
        ");
        $stmt->execute([$chat_id, $md5, $amount]);

        // Start immediate checking (first few checks)
        checkPaymentStatusAPI($chat_id, $md5, $amount);

    } catch (Exception $e) {
        error_log("Start API payment monitoring error: " . $e->getMessage());
    }
}

// Check payment status via Bakong API (like your Python auto_check_transaction)
function checkPaymentStatusAPI($chat_id, $md5, $amount) {
    global $pdo;

    error_log("DEBUG: Checking payment status for chat_id: $chat_id, md5: $md5, amount: $amount");

    $api_url = "https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5";
    $payload = json_encode(['md5' => $md5]);
    $headers = [
        'Content-Type: application/json',
        'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    error_log("DEBUG: Bakong API response - HTTP: $http_code, Response: $response");

    if ($http_code == 200 || $http_code == 201) {
        $response_data = json_decode($response, true);
        error_log("DEBUG: Bakong API parsed response: " . json_encode($response_data));

        if (isset($response_data['responseCode']) && $response_data['responseCode'] == 0) {
            // Payment successful! But check if already processed
            error_log("DEBUG: Payment successful! Checking if already processed...");

            // Check if this MD5 was already processed (prevent duplicate balance updates)
            $stmt = $pdo->prepare("SELECT status FROM bot_transactions WHERE md5 = ? AND status = 'completed'");
            $stmt->execute([$md5]);
            $already_processed = $stmt->fetch();

            if ($already_processed) {
                error_log("DEBUG: MD5 $md5 already processed, skipping to prevent duplicate");
                return true;
            }

            handleSuccessfulPayment($chat_id, $md5, $amount);
            return true;
        } else {
            error_log("DEBUG: Payment not found or pending. ResponseCode: " . ($response_data['responseCode'] ?? 'N/A'));
        }
    } else {
        error_log("DEBUG: Bakong API error - HTTP code: $http_code");
    }

    return false;
}

// Handle successful payment (like your Python success handling)
function handleSuccessfulPayment($chat_id, $md5, $amount) {
    global $pdo;

    try {
        // CRITICAL: Mark as completed FIRST to prevent race conditions
        $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
        $stmt->execute([$chat_id, $md5]);

        if ($stmt->rowCount() == 0) {
            error_log("Handle successful payment: Transaction already processed, skipping");
            return;
        }

        $bot = new TelegramBot(TELEGRAM_BOT_TOKEN, $pdo);

        // Send success message
        $bot->sendMessage($chat_id, "Payment of {$amount} USD confirmed! ✅");

        // IMMEDIATE BALANCE UPDATE - RIGHT HERE, RIGHT NOW
        error_log("IMMEDIATE: About to update balance for chat_id=$chat_id, amount=$amount");

        try {
            // Get user
            $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
            $stmt->execute([$chat_id]);
            $user = $stmt->fetch();

            if ($user) {
                error_log("IMMEDIATE: Found user ID {$user['id']}, current balance: {$user['balance']}");

                // Update balance directly
                $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
                $update_result = $stmt->execute([$amount, $user['id']]);

                if ($update_result) {
                    // Get new balance
                    $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
                    $stmt->execute([$user['id']]);
                    $new_balance = $stmt->fetchColumn();

                    error_log("IMMEDIATE: Balance updated! Old: {$user['balance']}, New: $new_balance");

                    // Record transaction
                    $stmt = $pdo->prepare("
                        INSERT INTO balance_transactions
                        (telegram_user_id, transaction_type, amount, description)
                        VALUES (?, 'topup', ?, ?)
                    ");
                    $stmt->execute([$user['id'], $amount, "Payment Confirmed - MD5: $md5"]);

                    error_log("IMMEDIATE: Transaction recorded successfully");

                    // Send confirmation with new balance
                    $bot->sendMessage($chat_id, "💰 Balance updated! New balance: $new_balance USD");

                } else {
                    error_log("IMMEDIATE: Failed to update balance!");
                }
            } else {
                error_log("IMMEDIATE: User not found for chat_id: $chat_id");
            }
        } catch (Exception $e) {
            error_log("IMMEDIATE: Balance update error: " . $e->getMessage());
        }

        // Delete QR code message
        $stmt = $pdo->prepare("SELECT message_id FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        $transaction = $stmt->fetch();

        if ($transaction && $transaction['message_id']) {
            try {
                $bot->deleteMessage($chat_id, $transaction['message_id']);
            } catch (Exception $e) {
                error_log("Delete message error: " . $e->getMessage());
            }
        }

        // Update user balance directly - FORCE UPDATE
        $balance_updated = forceUpdateBalance($chat_id, $amount, $md5);

        if ($balance_updated) {
            error_log("Handle successful payment: Balance updated successfully");
        } else {
            error_log("Handle successful payment: Balance update FAILED!");
        }

        // Remove from monitoring
        $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);

    } catch (Exception $e) {
        error_log("Handle successful payment error: " . $e->getMessage());
    }
}

// Update user balance
function updateUserBalance($chat_id, $amount) {
    global $pdo;

    error_log("DEBUG: Updating balance for chat_id: $chat_id, amount: $amount");

    try {
        // Get telegram_user_id from chat_id
        $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
        $stmt->execute([$chat_id]);
        $user = $stmt->fetch();

        if ($user) {
            error_log("DEBUG: Found user ID: {$user['id']}, current balance: {$user['balance']}");

            $pdo->beginTransaction();

            // Update balance
            $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
            $stmt->execute([$amount, $user['id']]);

            // Record transaction
            $stmt = $pdo->prepare("
                INSERT INTO balance_transactions
                (telegram_user_id, transaction_type, amount, description)
                VALUES (?, 'topup', ?, 'Top-up via KHQR API')
            ");
            $stmt->execute([$user['id'], $amount]);

            $pdo->commit();

            // Get new balance
            $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $new_balance = $stmt->fetchColumn();

            error_log("DEBUG: Balance updated successfully. New balance: $new_balance");
        } else {
            error_log("DEBUG: User not found for chat_id: $chat_id");
        }

    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Update user balance error: " . $e->getMessage());
    }
}

// Auto-check for completed payments (aggressive checking on every message)
function autoCheckCompletedPayments($chat_id) {
    global $pdo;

    try {
        // Get all pending transactions for this user
        $stmt = $pdo->prepare("
            SELECT * FROM bot_transactions
            WHERE chat_id = ? AND status = 'pending'
            ORDER BY created_at DESC
        ");
        $stmt->execute([$chat_id]);
        $pending_transactions = $stmt->fetchAll();

        foreach ($pending_transactions as $transaction) {
            $md5 = $transaction['md5'];
            $amount = $transaction['amount'];

            // Check payment status via API
            $api_url = "https://api.kunchhunlichhean.org/khqr/check?md5=" . urlencode($md5);

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);

            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($http_code == 200) {
                $response_data = json_decode($response, true);

                if (isset($response_data['responseCode']) && $response_data['responseCode'] == 0) {
                    // PAYMENT FOUND! Process it immediately
                    error_log("Auto-check: PAYMENT DETECTED for MD5: $md5, Amount: $amount");

                    // Mark as completed to prevent duplicates
                    $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
                    $stmt->execute([$chat_id, $md5]);

                    if ($stmt->rowCount() > 0) {
                        // Update user balance
                        updateUserBalance($chat_id, $amount);

                        // Send success message
                        $bot = new TelegramBot(TELEGRAM_BOT_TOKEN, $pdo);
                        $bot->sendMessage($chat_id, "✅ Payment of {$amount} USD confirmed!\n💰 Balance updated automatically!");

                        // Delete QR message if it exists
                        if ($transaction['message_id']) {
                            try {
                                $bot->deleteMessage($chat_id, $transaction['message_id']);
                                error_log("Auto-check: QR message deleted after payment confirmation");
                            } catch (Exception $e) {
                                error_log("Auto-check: Failed to delete QR message - " . $e->getMessage());
                            }
                        }

                        // Remove from payment monitoring
                        $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
                        $stmt->execute([$chat_id, $md5]);

                        error_log("Auto-check: Payment processed successfully for chat_id: $chat_id, amount: $amount");
                    }
                }
            }
        }

    } catch (Exception $e) {
        error_log("Auto-check completed payments error: " . $e->getMessage());
    }
}

// Check for expired QR codes and delete them
function checkExpiredQRCodes($chat_id) {
    global $pdo;

    try {
        // Get expired QR codes for this user
        $stmt = $pdo->prepare("
            SELECT qds.*, bt.message_id, bt.amount
            FROM qr_deletion_schedule qds
            LEFT JOIN bot_transactions bt ON qds.chat_id = bt.chat_id AND qds.md5 = bt.md5
            WHERE qds.chat_id = ?
            AND qds.delete_at <= NOW()
            AND qds.processed = 0
            AND bt.status = 'pending'
        ");
        $stmt->execute([$chat_id]);
        $expired_qrs = $stmt->fetchAll();

        foreach ($expired_qrs as $qr) {
            error_log("Processing expired QR: chat_id={$qr['chat_id']}, md5={$qr['md5']}");

            // Delete QR message from Telegram
            if ($qr['message_id']) {
                try {
                    $bot_token = TELEGRAM_BOT_TOKEN;
                    $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";

                    $data = [
                        'chat_id' => $qr['chat_id'],
                        'message_id' => $qr['message_id']
                    ];

                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $api_url);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);

                    if ($http_code == 200) {
                        error_log("QR message deleted successfully for MD5: {$qr['md5']}");
                    } else {
                        error_log("Failed to delete QR message for MD5: {$qr['md5']}, HTTP: $http_code");
                    }
                } catch (Exception $e) {
                    error_log("Delete QR message error: " . $e->getMessage());
                }
            }

            // Update transaction status to expired
            $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE chat_id = ? AND md5 = ?");
            $stmt->execute([$qr['chat_id'], $qr['md5']]);

            // Mark deletion as processed
            $stmt = $pdo->prepare("UPDATE qr_deletion_schedule SET processed = 1 WHERE id = ?");
            $stmt->execute([$qr['id']]);

            // Send expiration message
            $bot_token = TELEGRAM_BOT_TOKEN;
            $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

            $data = [
                'chat_id' => $qr['chat_id'],
                'text' => "⏰ QR code expired. Please generate a new one if needed.",
                'parse_mode' => 'HTML'
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

            $response = curl_exec($ch);
            curl_close($ch);

            error_log("Expiration message sent for MD5: {$qr['md5']}");
        }

    } catch (Exception $e) {
        error_log("Check expired QR codes error: " . $e->getMessage());
    }
}



// Start background process to delete QR after 3 minutes (like your Python threading.Thread)
function startBackgroundQRDeletion($chat_id, $md5) {
    try {
        // Store QR deletion timer in database instead of background process
        global $pdo;

        $delete_at = date('Y-m-d H:i:s', time() + 180); // 3 minutes from now

        $stmt = $pdo->prepare("
            INSERT INTO qr_deletion_schedule (chat_id, md5, delete_at)
            VALUES (?, ?, ?)
            ON DUPLICATE KEY UPDATE delete_at = VALUES(delete_at)
        ");
        $stmt->execute([$chat_id, $md5, $delete_at]);

        error_log("QR deletion scheduled for chat_id: $chat_id, md5: $md5 at $delete_at");

    } catch (Exception $e) {
        error_log("Start QR deletion scheduling error: " . $e->getMessage());
    }
}

// FORCE UPDATE BALANCE - NO CHECKS, JUST UPDATE
function forceUpdateBalance($chat_id, $amount, $md5) {
    global $pdo;

    error_log("FORCE BALANCE UPDATE: chat_id=$chat_id, amount=$amount, md5=$md5");

    try {
        // Get user ID
        $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
        $stmt->execute([$chat_id]);
        $user = $stmt->fetch();

        if (!$user) {
            error_log("FORCE BALANCE UPDATE: User not found!");
            return false;
        }

        $old_balance = $user['balance'];
        error_log("FORCE BALANCE UPDATE: User ID {$user['id']}, old balance: $old_balance");

        // FORCE UPDATE - NO DUPLICATE CHECKS
        $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
        $result = $stmt->execute([$amount, $user['id']]);

        if ($result) {
            // Get new balance to confirm
            $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $new_balance = $stmt->fetchColumn();

            error_log("FORCE BALANCE UPDATE: SUCCESS! Old: $old_balance, New: $new_balance");

            // Record transaction
            $stmt = $pdo->prepare("
                INSERT INTO balance_transactions
                (telegram_user_id, transaction_type, amount, description)
                VALUES (?, 'topup', ?, ?)
            ");
            $stmt->execute([$user['id'], $amount, "FORCE UPDATE - MD5: $md5"]);

            return true;
        } else {
            error_log("FORCE BALANCE UPDATE: UPDATE FAILED!");
            return false;
        }

    } catch (Exception $e) {
        error_log("FORCE BALANCE UPDATE ERROR: " . $e->getMessage());
        return false;
    }
}

// Update user balance directly - SIMPLE AND WORKS
function updateUserBalanceDirectly($chat_id, $amount, $md5) {
    global $pdo;

    error_log("BALANCE UPDATE: Starting for chat_id: $chat_id, amount: $amount, md5: $md5");

    try {
        // Get user
        $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
        $stmt->execute([$chat_id]);
        $user = $stmt->fetch();

        if (!$user) {
            error_log("BALANCE UPDATE: User not found for chat_id: $chat_id");
            return;
        }

        error_log("BALANCE UPDATE: Found user ID: {$user['id']}, current balance: {$user['balance']}");

        // Check if already processed this MD5
        $stmt = $pdo->prepare("SELECT id FROM balance_transactions WHERE description LIKE ? AND telegram_user_id = ?");
        $stmt->execute(["%$md5%", $user['id']]);
        $existing = $stmt->fetch();

        if ($existing) {
            error_log("BALANCE UPDATE: Already processed MD5: $md5, skipping");
            return;
        }

        // Update balance
        $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
        $result = $stmt->execute([$amount, $user['id']]);

        if ($result) {
            error_log("BALANCE UPDATE: Balance updated successfully");

            // Record transaction
            $stmt = $pdo->prepare("
                INSERT INTO balance_transactions
                (telegram_user_id, transaction_type, amount, description)
                VALUES (?, 'topup', ?, ?)
            ");
            $stmt->execute([$user['id'], $amount, "KHQR Payment - MD5: $md5"]);

            // Get new balance
            $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $new_balance = $stmt->fetchColumn();

            error_log("BALANCE UPDATE: SUCCESS! New balance: $new_balance");
        } else {
            error_log("BALANCE UPDATE: Failed to update balance");
        }

    } catch (Exception $e) {
        error_log("BALANCE UPDATE ERROR: " . $e->getMessage());
    }
}

// Update user balance ONCE per MD5 (prevent duplicates)
function updateUserBalanceOnce($chat_id, $amount, $md5) {
    global $pdo;

    error_log("DEBUG: Updating balance ONCE for chat_id: $chat_id, amount: $amount, md5: $md5");

    try {
        // Get telegram_user_id from chat_id
        $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
        $stmt->execute([$chat_id]);
        $user = $stmt->fetch();

        if ($user) {
            error_log("DEBUG: Found user ID: {$user['id']}, current balance: {$user['balance']}");

            $pdo->beginTransaction();

            // Check if balance transaction already exists for this MD5
            $stmt = $pdo->prepare("SELECT id FROM balance_transactions WHERE description LIKE ? AND telegram_user_id = ?");
            $stmt->execute(["%$md5%", $user['id']]);
            $existing_balance_tx = $stmt->fetch();

            if (!$existing_balance_tx) {
                // Update balance
                $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
                $stmt->execute([$amount, $user['id']]);

                // Record transaction with MD5 in description for tracking
                $stmt = $pdo->prepare("
                    INSERT INTO balance_transactions
                    (telegram_user_id, transaction_type, amount, description)
                    VALUES (?, 'topup', ?, ?)
                ");
                $stmt->execute([$user['id'], $amount, "Top-up via KHQR API - MD5: $md5"]);

                $pdo->commit();

                // Get new balance
                $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
                $stmt->execute([$user['id']]);
                $new_balance = $stmt->fetchColumn();

                error_log("DEBUG: Balance updated successfully. New balance: $new_balance (MD5: $md5)");
            } else {
                $pdo->rollBack();
                error_log("DEBUG: Balance already updated for MD5: $md5, skipping duplicate");
            }
        } else {
            error_log("DEBUG: User not found for chat_id: $chat_id");
        }

    } catch (Exception $e) {
        $pdo->rollBack();
        error_log("Update user balance once error: " . $e->getMessage());
    }
}

// Check pending payments on every bot interaction (simpler than background process)
function checkPendingPayments($chat_id) {
    global $pdo;

    try {
        // Get pending transactions for this user (max 2 minutes old to avoid checking very old ones)
        $stmt = $pdo->prepare("
            SELECT * FROM bot_transactions
            WHERE chat_id = ?
            AND status = 'pending'
            AND created_at > DATE_SUB(NOW(), INTERVAL 2 MINUTE)
            ORDER BY created_at DESC
        ");
        $stmt->execute([$chat_id]);
        $pending_transactions = $stmt->fetchAll();

        foreach ($pending_transactions as $transaction) {
            // Check payment status via Bakong API
            if (checkPaymentStatusAPI($chat_id, $transaction['md5'], $transaction['amount'])) {
                error_log("Auto-check: Payment found for MD5: {$transaction['md5']}");
                // Payment will be handled by checkPaymentStatusAPI function
                break; // Only process one payment at a time
            }
        }

    } catch (Exception $e) {
        error_log("Check pending payments error: " . $e->getMessage());
    }
}

// Start auto payment checking (checks every 2 seconds)
function startAutoPaymentChecking($chat_id, $md5, $amount) {
    try {
        if (function_exists('exec')) {
            $script_path = __DIR__ . '/auto_check_payment.php';
            $command = "php \"$script_path\" $chat_id $md5 $amount";

            // Run in background
            if (PHP_OS_FAMILY === 'Windows') {
                $command = "start /B $command > NUL 2>&1";
            } else {
                $command = "$command > /dev/null 2>&1 &";
            }

            exec($command);
            error_log("Auto payment checking started: chat_id=$chat_id, md5=$md5");
        } else {
            error_log("exec() not available - auto-checking disabled");
        }

    } catch (Exception $e) {
        error_log("Start auto payment checking error: " . $e->getMessage());
    }
}

// Main update handler function
function handleUpdate($bot, $update) {
    // Handle different types of updates
    if (isset($update['message'])) {
        handleMessage($bot, $update['message']);
    } elseif (isset($update['callback_query'])) {
        handleCallbackQuery($bot, $update['callback_query']);
    }
}

// Handle incoming webhook
$input = file_get_contents('php://input');
$update = json_decode($input, true);

if ($update) {
    $bot = new TelegramBot(TELEGRAM_BOT_TOKEN, $pdo);
    handleUpdate($bot, $update);
}

?>