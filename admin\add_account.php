<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$error_message = '';
$success_message = '';

// Check for success message from redirect
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $success_message = '✅ Account added successfully!';
}

// Helper function to get form values with defaults
function getFormValue($field, $default = '') {
    return htmlspecialchars($_POST[$field] ?? $default);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token.';
    } else {
        $title = sanitize_input($_POST['title'] ?? '');
        $description = sanitize_input($_POST['description'] ?? '');
        $price = floatval($_POST['price'] ?? 0);
        $account_code = sanitize_input($_POST['account_code'] ?? '');
        $game_type = sanitize_input($_POST['game_type'] ?? 'Mobile Legend');
        $account_data = $_POST['account_data'] ?? '';
        $account_status = sanitize_input($_POST['account_status'] ?? 'Good');
        $verify_code = sanitize_input($_POST['verify_code'] ?? 'Bug Code');
        $inactive_days = sanitize_input($_POST['inactive_days'] ?? '4Days+');
        $collector_status = sanitize_input($_POST['collector_status'] ?? 'Exalted');
        $device_info = sanitize_input($_POST['device_info'] ?? 'IDK');
        $rank_tier = sanitize_input($_POST['rank_tier'] ?? 'Legend');
        $additional_info = $_POST['additional_info'] ?? '';

        if (empty($title) || empty($price) || empty($account_data)) {
            $error_message = 'Please fill in all required fields.';
        } else {
            // Handle image upload
            $image_path = null;
            if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
                $file = $_FILES['image'];
                $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                
                if (!in_array($file_extension, ALLOWED_IMAGE_TYPES)) {
                    $error_message = 'Invalid image type. Allowed: ' . implode(', ', ALLOWED_IMAGE_TYPES);
                } elseif ($file['size'] > MAX_FILE_SIZE) {
                    $error_message = 'Image too large. Maximum size: ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB';
                } else {
                    $filename = uniqid() . '.' . $file_extension;
                    $upload_path = UPLOAD_PATH . 'accounts/' . $filename;
                    
                    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                        $image_path = 'accounts/' . $filename;
                    } else {
                        $error_message = 'Failed to upload image.';
                    }
                }
            }

            if (empty($error_message)) {
                try {
                    $stmt = $pdo->prepare("
                        INSERT INTO accounts (
                            title, description, price, account_code, account_data,
                            account_status, verify_code, inactive_days, collector_status,
                            device_info, additional_info, image_path, status
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'available')
                    ");

                    $stmt->execute([
                        $title, $description, $price, $account_code, $account_data,
                        $account_status, $verify_code, $inactive_days, $collector_status,
                        $device_info, $additional_info, $image_path
                    ]);
                    
                    $success_message = '✅ Account added successfully!';

                    // Clear form data after successful submission
                    $_POST = [];

                    // Redirect to prevent resubmission
                    header('Location: add_account.php?success=1');
                    exit;
                    
                } catch (PDOException $e) {
                    $error_message = 'Database error: ' . $e->getMessage();
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add New Account - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --sidebar-bg: #1e293b;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border: #e5e7eb;
            --hover: #f3f4f6;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            transform: translateX(4px);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .back-btn {
            background: var(--text-secondary);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .back-btn:hover {
            background: var(--text-primary);
            transform: translateY(-2px);
        }
        
        /* Form Container */
        .content {
            padding: 2rem;
            width: 100%;
            max-width: none;
        }
        
        .form-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.875rem 1rem;
            border: 2px solid var(--border);
            border-radius: 12px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }
        
        .file-upload input[type="file"] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }
        
        .file-upload-label {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            padding: 2rem;
            border: 2px dashed var(--border);
            border-radius: 12px;
            background: var(--hover);
            transition: all 0.3s ease;
        }
        
        .file-upload:hover .file-upload-label {
            border-color: var(--primary);
            background: rgba(59, 130, 246, 0.05);
        }
        
        .submit-btn {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            padding: 1rem 2rem;
            border: none;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .content {
                padding: 1rem;
            }

            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link active">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="balance_management.php" class="nav-link">
                    <i class="fas fa-wallet"></i>
                    Balance Management
                </a>
            </div>
            <div class="nav-item">
                <a href="topup_settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Top-Up Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Add New Account</h1>
            <a href="accounts.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Back to Accounts
            </a>
        </div>
        
        <div class="content">
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success_message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            <?php endif; ?>
            
            <div class="form-card">
                <form method="POST" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label" for="title">Account Title *</label>
                            <input type="text" id="title" name="title" class="form-input" required
                                   value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>"
                                   placeholder="e.g., Premium ML Account">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="account_code">Account Code *</label>
                            <input type="text" id="account_code" name="account_code" class="form-input" required
                                   value="<?php echo htmlspecialchars($_POST['account_code'] ?? ''); ?>"
                                   placeholder="e.g., ML001">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="game_type">Game Type</label>
                            <select id="game_type" name="game_type" class="form-select">
                                <option value="Mobile Legend" <?php echo ($_POST['game_type'] ?? '') === 'Mobile Legend' ? 'selected' : ''; ?>>Mobile Legend</option>
                                <option value="PUBG Mobile" <?php echo ($_POST['game_type'] ?? '') === 'PUBG Mobile' ? 'selected' : ''; ?>>PUBG Mobile</option>
                                <option value="Free Fire" <?php echo ($_POST['game_type'] ?? '') === 'Free Fire' ? 'selected' : ''; ?>>Free Fire</option>
                                <option value="Call of Duty" <?php echo ($_POST['game_type'] ?? '') === 'Call of Duty' ? 'selected' : ''; ?>>Call of Duty</option>
                                <option value="Other" <?php echo ($_POST['game_type'] ?? '') === 'Other' ? 'selected' : ''; ?>>Other</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="price">Price (USD) *</label>
                            <input type="number" id="price" name="price" class="form-input" required step="0.01" min="0"
                                   value="<?php echo htmlspecialchars($_POST['price'] ?? ''); ?>"
                                   placeholder="29.99">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="account_status">Account Status</label>
                            <input type="text" id="account_status" name="account_status" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['account_status'] ?? 'Good'); ?>"
                                   placeholder="Good">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="rank_tier">Rank/Tier</label>
                            <input type="text" id="rank_tier" name="rank_tier" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['rank_tier'] ?? 'Legend'); ?>"
                                   placeholder="Legend">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="verify_code">Verify Code</label>
                            <input type="text" id="verify_code" name="verify_code" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['verify_code'] ?? 'Bug Code'); ?>"
                                   placeholder="Bug Code">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="inactive_days">Inactive Period</label>
                            <input type="text" id="inactive_days" name="inactive_days" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['inactive_days'] ?? '4Days+'); ?>"
                                   placeholder="4Days+">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="collector_status">Collector Status</label>
                            <input type="text" id="collector_status" name="collector_status" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['collector_status'] ?? 'Exalted'); ?>"
                                   placeholder="Exalted">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label" for="device_info">Device Info</label>
                            <input type="text" id="device_info" name="device_info" class="form-input"
                                   value="<?php echo htmlspecialchars($_POST['device_info'] ?? 'IDK'); ?>"
                                   placeholder="IDK">
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="description">Description</label>
                            <textarea id="description" name="description" class="form-textarea"
                                      placeholder="Account description..."><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="account_data">Account Data (Login Details) *</label>
                            <textarea id="account_data" name="account_data" class="form-textarea" required
                                      placeholder="Username: <EMAIL>&#10;Password: password123&#10;Additional login info..."><?php echo htmlspecialchars($_POST['account_data'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label" for="additional_info">Additional Information</label>
                            <textarea id="additional_info" name="additional_info" class="form-textarea"
                                      placeholder="Any additional notes or information..."><?php echo htmlspecialchars($_POST['additional_info'] ?? ''); ?></textarea>
                        </div>
                        
                        <div class="form-group full-width">
                            <label class="form-label">Account Image</label>
                            <div class="file-upload">
                                <input type="file" name="image" accept="image/*">
                                <div class="file-upload-label">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Click to upload account screenshot</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="submit-btn">
                        <i class="fas fa-plus-circle"></i>
                        Add Account
                    </button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>
