<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$message = '';
$error = '';

// Handle settings update
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        try {
            $settings = [
                'min_topup_amount' => (float)($_POST['min_topup_amount'] ?? 10),
                'max_topup_amount' => (float)($_POST['max_topup_amount'] ?? 1000),
                'topup_enabled' => isset($_POST['topup_enabled']) ? '1' : '0',
                'qr_expiry_minutes' => (int)($_POST['qr_expiry_minutes'] ?? 30),
                'payment_gateway' => sanitize_input($_POST['payment_gateway'] ?? 'manual'),
                'currency' => sanitize_input($_POST['currency'] ?? 'USD'),
                'admin_topup_enabled' => isset($_POST['admin_topup_enabled']) ? '1' : '0'
            ];
            
            foreach ($settings as $name => $value) {
                $stmt = $pdo->prepare("
                    INSERT INTO topup_settings (setting_name, setting_value) 
                    VALUES (?, ?) 
                    ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)
                ");
                $stmt->execute([$name, $value]);
            }
            
            $message = 'Settings updated successfully!';
        } catch (Exception $e) {
            $error = 'Error updating settings: ' . $e->getMessage();
        }
    }
}

// Get current settings
try {
    $stmt = $pdo->query("SELECT setting_name, setting_value FROM topup_settings");
    $settings_data = $stmt->fetchAll();
    
    $settings = [];
    foreach ($settings_data as $setting) {
        $settings[$setting['setting_name']] = $setting['setting_value'];
    }
    
    // Set defaults if not found
    $defaults = [
        'min_topup_amount' => '10.00',
        'max_topup_amount' => '1000.00',
        'topup_enabled' => '1',
        'qr_expiry_minutes' => '30',
        'payment_gateway' => 'manual',
        'currency' => 'USD',
        'admin_topup_enabled' => '1'
    ];
    
    foreach ($defaults as $key => $default) {
        if (!isset($settings[$key])) {
            $settings[$key] = $default;
        }
    }
} catch (PDOException $e) {
    $error = "Error loading settings: " . $e->getMessage();
    $settings = [];
}

// Get top-up statistics
try {
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_requests,
            COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_requests,
            COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_requests,
            COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_requests,
            COALESCE(SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END), 0) as total_amount
        FROM topup_requests
    ");
    $stats = $stmt->fetch();
} catch (PDOException $e) {
    $stats = [
        'total_requests' => 0,
        'completed_requests' => 0,
        'pending_requests' => 0,
        'failed_requests' => 0,
        'total_amount' => 0
    ];
}

// Get recent top-up requests
try {
    $stmt = $pdo->query("
        SELECT 
            tr.*,
            tu.username,
            tu.first_name,
            tu.last_name
        FROM topup_requests tr
        JOIN telegram_users tu ON tr.telegram_user_id = tu.id
        ORDER BY tr.created_at DESC
        LIMIT 10
    ");
    $recent_topups = $stmt->fetchAll();
} catch (PDOException $e) {
    $recent_topups = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Top-Up Settings - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --dark: #1f2937;
            --light: #f8fafc;
            --border: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --hover: #f3f4f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--dark);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: white;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.2s;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link.active {
            background: var(--primary);
            color: white;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }

        .top-bar {
            background: var(--bg-primary);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: var(--danger);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.2s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* Content */
        .content {
            padding: 2rem;
            width: 100%;
            max-width: none;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            margin: 0 auto 1rem;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: var(--primary);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border);
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.2s;
            background: white;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary);
        }

        .form-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .form-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: var(--primary);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }

        .table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .table tr:hover {
            background: var(--hover);
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }

        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content {
                padding: 1rem;
            }
            
            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }
        
        @media (max-width: 768px) {
            .form-grid, .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="balance_management.php" class="nav-link">
                    <i class="fas fa-wallet"></i>
                    Balance Management
                </a>
            </div>
            <div class="nav-item">
                <a href="topup_settings.php" class="nav-link active">
                    <i class="fas fa-cog"></i>
                    Top-Up Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Top-Up Settings</h1>
            <div class="user-menu">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--info);">
                        <i class="fas fa-receipt"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($stats['total_requests']); ?></div>
                    <div class="stat-label">Total Requests</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--success);">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($stats['completed_requests']); ?></div>
                    <div class="stat-label">Completed</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--warning);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value"><?php echo number_format($stats['pending_requests']); ?></div>
                    <div class="stat-label">Pending</div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary);">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="stat-value">$<?php echo number_format($stats['total_amount'], 2); ?></div>
                    <div class="stat-label">Total Amount</div>
                </div>
            </div>

            <!-- Settings Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div>
                        <div class="card-title">Top-Up Configuration</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Configure top-up limits and payment settings</div>
                    </div>
                </div>

                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Minimum Top-Up Amount</label>
                            <input type="number" name="min_topup_amount" class="form-input" step="0.01" min="0.01"
                                   value="<?php echo htmlspecialchars($settings['min_topup_amount'] ?? '10.00'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Maximum Top-Up Amount</label>
                            <input type="number" name="max_topup_amount" class="form-input" step="0.01" min="0.01"
                                   value="<?php echo htmlspecialchars($settings['max_topup_amount'] ?? '1000.00'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">QR Code Expiry (Minutes)</label>
                            <input type="number" name="qr_expiry_minutes" class="form-input" min="1" max="1440"
                                   value="<?php echo htmlspecialchars($settings['qr_expiry_minutes'] ?? '30'); ?>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Currency</label>
                            <select name="currency" class="form-select" required>
                                <option value="USD" <?php echo ($settings['currency'] ?? 'USD') === 'USD' ? 'selected' : ''; ?>>USD - US Dollar</option>
                                <option value="EUR" <?php echo ($settings['currency'] ?? 'USD') === 'EUR' ? 'selected' : ''; ?>>EUR - Euro</option>
                                <option value="GBP" <?php echo ($settings['currency'] ?? 'USD') === 'GBP' ? 'selected' : ''; ?>>GBP - British Pound</option>
                                <option value="JPY" <?php echo ($settings['currency'] ?? 'USD') === 'JPY' ? 'selected' : ''; ?>>JPY - Japanese Yen</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Payment Gateway</label>
                            <select name="payment_gateway" class="form-select" required>
                                <option value="manual" <?php echo ($settings['payment_gateway'] ?? 'manual') === 'manual' ? 'selected' : ''; ?>>Manual QR Code</option>
                                <option value="auto" <?php echo ($settings['payment_gateway'] ?? 'manual') === 'auto' ? 'selected' : ''; ?>>Automatic Payment</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" name="topup_enabled" id="topup_enabled"
                                   <?php echo ($settings['topup_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                            <label for="topup_enabled" class="form-label" style="margin-bottom: 0;">Enable Top-Up System</label>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Allow users to top-up their balance via Telegram bot</div>
                    </div>

                    <div class="form-group">
                        <div class="form-checkbox">
                            <input type="checkbox" name="admin_topup_enabled" id="admin_topup_enabled"
                                   <?php echo ($settings['admin_topup_enabled'] ?? '1') === '1' ? 'checked' : ''; ?>>
                            <label for="admin_topup_enabled" class="form-label" style="margin-bottom: 0;">Enable Admin Balance Adjustment</label>
                        </div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Allow admins to manually adjust user balances</div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Save Settings
                    </button>
                </form>
            </div>

            <!-- Recent Top-Up Requests -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div>
                        <div class="card-title">Recent Top-Up Requests</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Latest top-up requests from users</div>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Payment Method</th>
                                <th>Transaction ID</th>
                                <th>Created</th>
                                <th>Completed</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recent_topups)): ?>
                                <tr>
                                    <td colspan="7" style="text-align: center; color: var(--text-secondary);">
                                        No top-up requests found
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_topups as $topup): ?>
                                    <tr>
                                        <td>
                                            <div style="font-weight: 600;">
                                                <?php echo htmlspecialchars($topup['first_name'] . ' ' . $topup['last_name']); ?>
                                            </div>
                                            <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                                @<?php echo htmlspecialchars($topup['username'] ?: 'No username'); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <span style="font-weight: 600; color: var(--success);">
                                                $<?php echo number_format($topup['amount'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_colors = [
                                                'pending' => 'warning',
                                                'completed' => 'success',
                                                'failed' => 'danger',
                                                'expired' => 'danger'
                                            ];
                                            $color = $status_colors[$topup['status']] ?? 'info';
                                            ?>
                                            <span class="badge badge-<?php echo $color; ?>">
                                                <?php echo ucfirst($topup['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($topup['payment_method']); ?>
                                        </td>
                                        <td>
                                            <?php if ($topup['transaction_id']): ?>
                                                <code style="font-size: 0.875rem;"><?php echo htmlspecialchars($topup['transaction_id']); ?></code>
                                            <?php else: ?>
                                                <span style="color: var(--text-secondary);">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y H:i', strtotime($topup['created_at'])); ?>
                                        </td>
                                        <td>
                                            <?php if ($topup['completed_at']): ?>
                                                <?php echo date('M j, Y H:i', strtotime($topup['completed_at'])); ?>
                                            <?php else: ?>
                                                <span style="color: var(--text-secondary);">-</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
