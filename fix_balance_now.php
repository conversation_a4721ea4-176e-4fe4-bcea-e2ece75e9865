<?php
require_once 'config.php';

echo "<h1>🔧 Fix Balance Issue</h1>";

$chat_id = 1630035459; // Your Telegram ID

try {
    // Get user info
    $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
    $stmt->execute([$chat_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "<p style='color: red;'>❌ User not found for chat_id: $chat_id</p>";
        exit;
    }
    
    echo "<h2>👤 User Info</h2>";
    echo "<p><strong>User ID:</strong> {$user['id']}</p>";
    echo "<p><strong>Current Balance:</strong> {$user['balance']} USD</p>";
    
    // Get completed bot transactions that don't have balance updates
    $stmt = $pdo->prepare("
        SELECT bt.* 
        FROM bot_transactions bt
        LEFT JOIN balance_transactions blt ON blt.description LIKE CONCAT('%', bt.md5, '%') AND blt.telegram_user_id = ?
        WHERE bt.chat_id = ? 
        AND bt.status = 'completed' 
        AND blt.id IS NULL
        ORDER BY bt.created_at DESC
    ");
    $stmt->execute([$user['id'], $chat_id]);
    $missing_updates = $stmt->fetchAll();
    
    echo "<h2>💰 Missing Balance Updates</h2>";
    
    if ($missing_updates) {
        echo "<p style='color: orange;'>Found " . count($missing_updates) . " completed payments without balance updates:</p>";
        
        $total_missing = 0;
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>MD5</th><th>Amount</th><th>Date</th><th>Action</th></tr>";
        
        foreach ($missing_updates as $tx) {
            $total_missing += $tx['amount'];
            echo "<tr>";
            echo "<td>" . substr($tx['md5'], 0, 8) . "...</td>";
            echo "<td>{$tx['amount']} USD</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td><a href='?fix_md5={$tx['md5']}&amount={$tx['amount']}' style='color: green;'>Fix This</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Total Missing Amount:</strong> $total_missing USD</p>";
        echo "<p><a href='?fix_all=1' style='background: green; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🔧 Fix All Missing Balances</a></p>";
        
    } else {
        echo "<p style='color: green;'>✅ No missing balance updates found!</p>";
    }
    
    // Handle individual fix
    if (isset($_GET['fix_md5']) && isset($_GET['amount'])) {
        $md5 = $_GET['fix_md5'];
        $amount = floatval($_GET['amount']);
        
        echo "<h2>🔧 Fixing Balance for MD5: " . substr($md5, 0, 8) . "...</h2>";
        
        // Update balance
        $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
        $result = $stmt->execute([$amount, $user['id']]);
        
        if ($result) {
            // Record transaction
            $stmt = $pdo->prepare("
                INSERT INTO balance_transactions 
                (telegram_user_id, transaction_type, amount, description) 
                VALUES (?, 'topup', ?, ?)
            ");
            $stmt->execute([$user['id'], $amount, "Manual Fix - MD5: $md5"]);
            
            // Get new balance
            $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $new_balance = $stmt->fetchColumn();
            
            echo "<p style='color: green; background: #d4edda; padding: 10px; border-radius: 5px;'>";
            echo "✅ Balance updated successfully!<br>";
            echo "New Balance: $new_balance USD<br>";
            echo "Added: $amount USD";
            echo "</p>";
            
            echo "<p><a href='?' style='background: blue; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Page</a></p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to update balance</p>";
        }
    }
    
    // Handle fix all
    if (isset($_GET['fix_all'])) {
        echo "<h2>🔧 Fixing All Missing Balances</h2>";
        
        $total_fixed = 0;
        $count_fixed = 0;
        
        foreach ($missing_updates as $tx) {
            // Update balance
            $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
            $result = $stmt->execute([$tx['amount'], $user['id']]);
            
            if ($result) {
                // Record transaction
                $stmt = $pdo->prepare("
                    INSERT INTO balance_transactions 
                    (telegram_user_id, transaction_type, amount, description) 
                    VALUES (?, 'topup', ?, ?)
                ");
                $stmt->execute([$user['id'], $tx['amount'], "Bulk Fix - MD5: {$tx['md5']}"]);
                
                $total_fixed += $tx['amount'];
                $count_fixed++;
                
                echo "<p style='color: green;'>✅ Fixed MD5: " . substr($tx['md5'], 0, 8) . "... (+{$tx['amount']} USD)</p>";
            }
        }
        
        if ($count_fixed > 0) {
            // Get new balance
            $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
            $stmt->execute([$user['id']]);
            $new_balance = $stmt->fetchColumn();
            
            echo "<div style='color: green; background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
            echo "<h3>🎉 All Balances Fixed!</h3>";
            echo "<p><strong>Transactions Fixed:</strong> $count_fixed</p>";
            echo "<p><strong>Total Amount Added:</strong> $total_fixed USD</p>";
            echo "<p><strong>New Balance:</strong> $new_balance USD</p>";
            echo "</div>";
            
            echo "<p><a href='?' style='background: blue; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>🔄 Refresh Page</a></p>";
        }
    }
    
    // Show current balance transactions
    echo "<h2>📊 Recent Balance Transactions</h2>";
    $stmt = $pdo->prepare("
        SELECT * FROM balance_transactions 
        WHERE telegram_user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
    $stmt->execute([$user['id']]);
    $balance_txs = $stmt->fetchAll();
    
    if ($balance_txs) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Type</th><th>Amount</th><th>Description</th><th>Date</th></tr>";
        foreach ($balance_txs as $tx) {
            echo "<tr>";
            echo "<td>{$tx['transaction_type']}</td>";
            echo "<td>{$tx['amount']}</td>";
            echo "<td>" . substr($tx['description'], 0, 50) . "...</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No balance transactions found.</p>";
    }
    
    echo "<h2>🧪 Test Bot Balance</h2>";
    echo "<p>After fixing, test your bot:</p>";
    echo "<ol>";
    echo "<li>Send <strong>/balance</strong> to your bot</li>";
    echo "<li>Should show the updated balance</li>";
    echo "<li>Try a new <strong>/topup</strong> to test future payments</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
