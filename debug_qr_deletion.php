<?php
require_once 'config.php';

echo "<h2>🔍 Debug QR Deletion System</h2>";

$chat_id = 1630035459; // Your Telegram ID

try {
    echo "<h3>📋 Current Bot Transactions:</h3>";
    
    // Check bot transactions
    $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE chat_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$chat_id]);
    $transactions = $stmt->fetchAll();
    
    if ($transactions) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>MD5</th><th>Message ID</th><th>Amount</th><th>Status</th><th>Created</th><th>Age (min)</th></tr>";
        
        foreach ($transactions as $tx) {
            $age_minutes = (time() - strtotime($tx['created_at'])) / 60;
            $age_color = $age_minutes > 3 ? 'red' : 'green';
            
            echo "<tr>";
            echo "<td>" . substr($tx['md5'], 0, 8) . "...</td>";
            echo "<td>{$tx['message_id']}</td>";
            echo "<td>{$tx['amount']} USD</td>";
            echo "<td>{$tx['status']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td style='color: $age_color;'>" . round($age_minutes, 1) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No transactions found</p>";
    }
    
    echo "<h3>⏰ QR Deletion Schedule:</h3>";
    
    // Check QR deletion schedule
    $stmt = $pdo->prepare("SELECT * FROM qr_deletion_schedule WHERE chat_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$chat_id]);
    $schedules = $stmt->fetchAll();
    
    if ($schedules) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>MD5</th><th>Delete At</th><th>Processed</th><th>Created</th><th>Status</th></tr>";
        
        foreach ($schedules as $schedule) {
            $delete_time = strtotime($schedule['delete_at']);
            $now = time();
            $is_expired = $now > $delete_time;
            $status_color = $is_expired ? 'red' : 'green';
            $status_text = $is_expired ? 'EXPIRED' : 'WAITING';
            
            if ($schedule['processed']) {
                $status_text = 'PROCESSED';
                $status_color = 'blue';
            }
            
            echo "<tr>";
            echo "<td>" . substr($schedule['md5'], 0, 8) . "...</td>";
            echo "<td>{$schedule['delete_at']}</td>";
            echo "<td>" . ($schedule['processed'] ? 'Yes' : 'No') . "</td>";
            echo "<td>{$schedule['created_at']}</td>";
            echo "<td style='color: $status_color; font-weight: bold;'>$status_text</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No deletion schedules found</p>";
    }
    
    echo "<h3>🧪 Manual Test:</h3>";
    
    // Manual test button
    if (isset($_GET['test_deletion'])) {
        echo "<h4>🔄 Running Manual QR Deletion Check...</h4>";
        
        // Simulate the checkExpiredQRCodes function
        $stmt = $pdo->prepare("
            SELECT qds.*, bt.message_id, bt.amount 
            FROM qr_deletion_schedule qds
            LEFT JOIN bot_transactions bt ON qds.chat_id = bt.chat_id AND qds.md5 = bt.md5
            WHERE qds.chat_id = ? 
            AND qds.delete_at <= NOW() 
            AND qds.processed = 0
            AND bt.status = 'pending'
        ");
        $stmt->execute([$chat_id]);
        $expired_qrs = $stmt->fetchAll();
        
        if ($expired_qrs) {
            echo "<p style='color: orange;'>Found " . count($expired_qrs) . " expired QR(s) to process:</p>";
            
            foreach ($expired_qrs as $qr) {
                echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
                echo "<strong>MD5:</strong> " . substr($qr['md5'], 0, 8) . "...<br>";
                echo "<strong>Message ID:</strong> {$qr['message_id']}<br>";
                echo "<strong>Amount:</strong> {$qr['amount']} USD<br>";
                
                // Try to delete the message
                if ($qr['message_id']) {
                    $bot_token = TELEGRAM_BOT_TOKEN;
                    $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
                    
                    $data = [
                        'chat_id' => $qr['chat_id'],
                        'message_id' => $qr['message_id']
                    ];
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $api_url);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    echo "<strong>Delete API Response:</strong> HTTP $http_code<br>";
                    echo "<strong>Response:</strong> $response<br>";
                    
                    if ($http_code == 200) {
                        // Update transaction status
                        $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE chat_id = ? AND md5 = ?");
                        $stmt->execute([$qr['chat_id'], $qr['md5']]);
                        
                        // Mark as processed
                        $stmt = $pdo->prepare("UPDATE qr_deletion_schedule SET processed = 1 WHERE id = ?");
                        $stmt->execute([$qr['id']]);
                        
                        // Send expiration message
                        $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
                        $data = [
                            'chat_id' => $qr['chat_id'],
                            'text' => "⏰ QR code expired. Please generate a new one if needed."
                        ];
                        
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $api_url);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        
                        $response = curl_exec($ch);
                        curl_close($ch);
                        
                        echo "<p style='color: green;'>✅ QR deleted and expiration message sent!</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Failed to delete QR message</p>";
                    }
                }
                echo "</div>";
            }
        } else {
            echo "<p style='color: green;'>✅ No expired QRs found to process</p>";
        }
        
        echo "<p><a href='?' style='background: blue; color: white; padding: 10px; text-decoration: none;'>🔄 Refresh</a></p>";
    } else {
        echo "<p><a href='?test_deletion=1' style='background: red; color: white; padding: 10px; text-decoration: none;'>🧪 Test QR Deletion Now</a></p>";
    }
    
    echo "<h3>📝 Instructions:</h3>";
    echo "<ol>";
    echo "<li>Generate a QR code with <code>/topup</code> and small amount</li>";
    echo "<li>Wait 3+ minutes without paying</li>";
    echo "<li>Click 'Test QR Deletion Now' button above</li>";
    echo "<li>Check if QR message gets deleted from Telegram</li>";
    echo "<li>Check if you receive expiration message</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
