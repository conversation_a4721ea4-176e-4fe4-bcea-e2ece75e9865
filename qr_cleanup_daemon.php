<?php
// QR Cleanup Daemon - Runs continuously in background

require_once 'config.php';

echo "🤖 QR Cleanup Daemon Started\n";
echo "Press Ctrl+C to stop\n\n";

// Run forever
while (true) {
    try {
        echo "[" . date('Y-m-d H:i:s') . "] Checking for expired QR codes...\n";
        
        // Find all pending QR transactions older than 3 minutes
        $stmt = $pdo->prepare("
            SELECT * FROM bot_transactions 
            WHERE status = 'pending' 
            AND created_at <= DATE_SUB(NOW(), INTERVAL 3 MINUTE)
            ORDER BY created_at ASC
        ");
        $stmt->execute();
        $expired_transactions = $stmt->fetchAll();
        
        if (count($expired_transactions) > 0) {
            echo "Found " . count($expired_transactions) . " expired QR code(s)\n";
            
            foreach ($expired_transactions as $transaction) {
                $chat_id = $transaction['chat_id'];
                $md5 = $transaction['md5'];
                $message_id = $transaction['message_id'];
                
                echo "  Processing: chat_id=$chat_id, message_id=$message_id\n";
                
                // Delete QR message from Telegram
                if ($message_id) {
                    $bot_token = TELEGRAM_BOT_TOKEN;
                    $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
                    
                    $data = [
                        'chat_id' => $chat_id,
                        'message_id' => $message_id
                    ];
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $api_url);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    
                    $response = curl_exec($ch);
                    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                    curl_close($ch);
                    
                    if ($http_code == 200) {
                        echo "    ✅ QR message deleted\n";
                    } else {
                        echo "    ❌ Failed to delete QR message (HTTP $http_code)\n";
                    }
                }
                
                // Update transaction status to expired
                $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE id = ?");
                $stmt->execute([$transaction['id']]);
                
                // Remove from payment monitoring
                $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
                $stmt->execute([$chat_id, $md5]);
                
                // Send expiration message
                $bot_token = TELEGRAM_BOT_TOKEN;
                $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
                
                $data = [
                    'chat_id' => $chat_id,
                    'text' => "⏰ QR code expired. Please generate a new one if needed."
                ];
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                curl_exec($ch);
                curl_close($ch);
                
                echo "    📨 Expiration message sent\n";
            }
        } else {
            echo "No expired QR codes found\n";
        }
        
        echo "Waiting 60 seconds...\n\n";
        sleep(60); // Wait 1 minute
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        sleep(60); // Wait before retrying
    }
}
?>
