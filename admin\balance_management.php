<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$message = '';
$error = '';

// Handle balance adjustment
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'adjust_balance') {
            $telegram_user_id = (int)($_POST['telegram_user_id'] ?? 0);
            $amount = (float)($_POST['amount'] ?? 0);
            $description = sanitize_input($_POST['description'] ?? '');
            $adjustment_type = $_POST['adjustment_type'] ?? 'add';
            
            if ($telegram_user_id > 0 && $amount > 0) {
                try {
                    $pdo->beginTransaction();
                    
                    // Get current balance
                    $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
                    $stmt->execute([$telegram_user_id]);
                    $current_balance = $stmt->fetchColumn();
                    
                    if ($current_balance !== false) {
                        $balance_before = $current_balance;
                        
                        if ($adjustment_type === 'subtract') {
                            $amount = -$amount;
                        }
                        
                        $balance_after = $balance_before + $amount;
                        
                        // Prevent negative balance
                        if ($balance_after < 0) {
                            throw new Exception('Insufficient balance for this adjustment.');
                        }
                        
                        // Update user balance
                        $stmt = $pdo->prepare("UPDATE telegram_users SET balance = ? WHERE id = ?");
                        $stmt->execute([$balance_after, $telegram_user_id]);
                        
                        // Record transaction
                        $stmt = $pdo->prepare("
                            INSERT INTO balance_transactions 
                            (telegram_user_id, transaction_type, amount, balance_before, balance_after, description, admin_id) 
                            VALUES (?, 'admin_adjustment', ?, ?, ?, ?, ?)
                        ");
                        $stmt->execute([
                            $telegram_user_id, 
                            $amount, 
                            $balance_before, 
                            $balance_after, 
                            $description,
                            $_SESSION['admin_id']
                        ]);
                        
                        $pdo->commit();
                        $message = 'Balance adjusted successfully!';
                    } else {
                        throw new Exception('User not found.');
                    }
                } catch (Exception $e) {
                    $pdo->rollBack();
                    $error = 'Error adjusting balance: ' . $e->getMessage();
                }
            } else {
                $error = 'Please provide valid user and amount.';
            }
        }
    }
}

// Get telegram users with balances
try {
    $stmt = $pdo->query("
        SELECT 
            tu.id,
            tu.telegram_id,
            tu.username,
            tu.first_name,
            tu.last_name,
            tu.balance,
            tu.created_at,
            COUNT(bt.id) as transaction_count,
            COALESCE(SUM(CASE WHEN bt.transaction_type = 'topup' THEN bt.amount ELSE 0 END), 0) as total_topups
        FROM telegram_users tu
        LEFT JOIN balance_transactions bt ON tu.id = bt.telegram_user_id
        GROUP BY tu.id
        ORDER BY tu.balance DESC, tu.created_at DESC
    ");
    $users = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error loading users: " . $e->getMessage();
    $users = [];
}

// Get recent transactions
try {
    $stmt = $pdo->query("
        SELECT 
            bt.*,
            tu.username,
            tu.first_name,
            tu.last_name,
            au.username as admin_username
        FROM balance_transactions bt
        JOIN telegram_users tu ON bt.telegram_user_id = tu.id
        LEFT JOIN admin_users au ON bt.admin_id = au.id
        ORDER BY bt.created_at DESC
        LIMIT 20
    ");
    $recent_transactions = $stmt->fetchAll();
} catch (PDOException $e) {
    $error = "Error loading transactions: " . $e->getMessage();
    $recent_transactions = [];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Balance Management - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;
            --dark: #1f2937;
            --light: #f8fafc;
            --border: #e5e7eb;
            --text-primary: #111827;
            --text-secondary: #6b7280;
            --bg-primary: #ffffff;
            --bg-secondary: #f9fafb;
            --hover: #f3f4f6;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
        }

        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--dark);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-decoration: none;
            color: white;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: var(--primary);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .logo-text {
            font-size: 1.25rem;
            font-weight: 600;
        }

        .nav-menu {
            padding: 1rem 0;
        }

        .nav-item {
            margin: 0.25rem 1rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem 1rem;
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            border-radius: 10px;
            transition: all 0.2s;
            font-weight: 500;
        }

        .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .nav-link.active {
            background: var(--primary);
            color: white;
        }

        .nav-link i {
            width: 20px;
            text-align: center;
        }

        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }

        .top-bar {
            background: var(--bg-primary);
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .page-title {
            font-size: 1.875rem;
            font-weight: 700;
            color: var(--text-primary);
        }

        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .logout-btn {
            background: var(--danger);
            color: white;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: background 0.2s;
        }

        .logout-btn:hover {
            background: #dc2626;
        }

        /* Content */
        .content {
            padding: 2rem;
            width: 100%;
            max-width: none;
        }

        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            font-weight: 500;
        }

        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            width: 48px;
            height: 48px;
            background: var(--primary);
            color: white;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 2px solid var(--border);
            border-radius: 10px;
            font-size: 1rem;
            transition: border-color 0.2s;
            background: white;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--primary);
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background: #2563eb;
            transform: translateY(-2px);
        }

        .btn-success {
            background: var(--success);
            color: white;
        }

        .btn-success:hover {
            background: #059669;
        }

        .btn-danger {
            background: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background: #dc2626;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1rem;
        }

        .table th,
        .table td {
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }

        .table th {
            background: var(--bg-secondary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .table tr:hover {
            background: var(--hover);
        }

        .badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .badge-success {
            background: #d1fae5;
            color: #065f46;
        }

        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .content {
                padding: 1rem;
            }
            
            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }
        
        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-gamepad"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="balance_management.php" class="nav-link active">
                    <i class="fas fa-wallet"></i>
                    Balance Management
                </a>
            </div>
            <div class="nav-item">
                <a href="topup_settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Top-Up Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Balance Management</h1>
            <div class="user-menu">
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </a>
            </div>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Balance Adjustment Form -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-plus-minus"></i>
                    </div>
                    <div>
                        <div class="card-title">Adjust User Balance</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Add or subtract balance from user accounts</div>
                    </div>
                </div>
                
                <form method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <input type="hidden" name="action" value="adjust_balance">
                    
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">Select User *</label>
                            <select name="telegram_user_id" class="form-select" required>
                                <option value="">Choose a user...</option>
                                <?php foreach ($users as $user): ?>
                                    <option value="<?php echo $user['id']; ?>">
                                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                        (@<?php echo htmlspecialchars($user['username'] ?: 'No username'); ?>)
                                        - Balance: $<?php echo number_format($user['balance'], 2); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Amount *</label>
                            <input type="number" name="amount" class="form-input" step="0.01" min="0.01" required placeholder="0.00">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Adjustment Type *</label>
                            <select name="adjustment_type" class="form-select" required>
                                <option value="add">Add Balance</option>
                                <option value="subtract">Subtract Balance</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <input type="text" name="description" class="form-input" placeholder="Reason for adjustment...">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Adjust Balance
                    </button>
                </form>
            </div>

            <!-- Users with Balances -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <div class="card-title">User Balances</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Overview of all user balances and activity</div>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Username</th>
                                <th>Balance</th>
                                <th>Total Top-ups</th>
                                <th>Transactions</th>
                                <th>Joined</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($users)): ?>
                                <tr>
                                    <td colspan="6" style="text-align: center; color: var(--text-secondary);">
                                        No users found
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div style="font-weight: 600;">
                                                <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                                            </div>
                                            <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                                ID: <?php echo $user['telegram_id']; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($user['username']): ?>
                                                @<?php echo htmlspecialchars($user['username']); ?>
                                            <?php else: ?>
                                                <span style="color: var(--text-secondary);">No username</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span style="font-weight: 600; color: <?php echo $user['balance'] > 0 ? 'var(--success)' : 'var(--text-secondary)'; ?>;">
                                                $<?php echo number_format($user['balance'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span style="color: var(--info);">
                                                $<?php echo number_format($user['total_topups'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                <?php echo $user['transaction_count']; ?> transactions
                                            </span>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Transactions -->
            <div class="card">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-history"></i>
                    </div>
                    <div>
                        <div class="card-title">Recent Transactions</div>
                        <div style="color: var(--text-secondary); font-size: 0.875rem;">Latest balance transactions and adjustments</div>
                    </div>
                </div>

                <div style="overflow-x: auto;">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>User</th>
                                <th>Type</th>
                                <th>Amount</th>
                                <th>Balance Change</th>
                                <th>Description</th>
                                <th>Admin</th>
                                <th>Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($recent_transactions)): ?>
                                <tr>
                                    <td colspan="7" style="text-align: center; color: var(--text-secondary);">
                                        No transactions found
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($recent_transactions as $transaction): ?>
                                    <tr>
                                        <td>
                                            <div style="font-weight: 600;">
                                                <?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?>
                                            </div>
                                            <div style="font-size: 0.875rem; color: var(--text-secondary);">
                                                @<?php echo htmlspecialchars($transaction['username'] ?: 'No username'); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $type_colors = [
                                                'topup' => 'success',
                                                'purchase' => 'warning',
                                                'refund' => 'info',
                                                'admin_adjustment' => 'danger'
                                            ];
                                            $color = $type_colors[$transaction['transaction_type']] ?? 'info';
                                            ?>
                                            <span class="badge badge-<?php echo $color; ?>">
                                                <?php echo ucfirst(str_replace('_', ' ', $transaction['transaction_type'])); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span style="color: <?php echo $transaction['amount'] >= 0 ? 'var(--success)' : 'var(--danger)'; ?>; font-weight: 600;">
                                                <?php echo $transaction['amount'] >= 0 ? '+' : ''; ?>$<?php echo number_format($transaction['amount'], 2); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div style="font-size: 0.875rem;">
                                                $<?php echo number_format($transaction['balance_before'], 2); ?> →
                                                $<?php echo number_format($transaction['balance_after'], 2); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php echo htmlspecialchars($transaction['description'] ?: '-'); ?>
                                        </td>
                                        <td>
                                            <?php if ($transaction['admin_username']): ?>
                                                <span style="color: var(--primary);">
                                                    <?php echo htmlspecialchars($transaction['admin_username']); ?>
                                                </span>
                                            <?php else: ?>
                                                <span style="color: var(--text-secondary);">System</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo date('M j, Y H:i', strtotime($transaction['created_at'])); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
