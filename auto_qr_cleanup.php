<!DOCTYPE html>
<html>
<head>
    <title>Auto QR Cleanup</title>
    <meta http-equiv="refresh" content="60">
</head>
<body>
    <h2>🔄 Auto QR Cleanup Running</h2>
    <p><strong>Current Time:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <?php
    // Run the cleanup script
    ob_start();
    include 'qr_cleanup.php';
    $cleanup_output = ob_get_clean();
    
    echo "<p><strong>Cleanup Status:</strong> ✅ Completed</p>";
    echo "<p><strong>Next Check:</strong> " . date('Y-m-d H:i:s', time() + 60) . "</p>";
    
    // Show recent transactions
    require_once 'config.php';
    
    try {
        $stmt = $pdo->prepare("SELECT * FROM bot_transactions ORDER BY created_at DESC LIMIT 5");
        $stmt->execute();
        $transactions = $stmt->fetchAll();
        
        echo "<h3>📋 Recent Transactions:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Chat ID</th><th>Amount</th><th>Status</th><th>Created</th><th>Age</th></tr>";
        
        foreach ($transactions as $tx) {
            $age_minutes = (time() - strtotime($tx['created_at'])) / 60;
            $age_color = $tx['status'] === 'pending' && $age_minutes > 3 ? 'red' : 'green';
            
            echo "<tr>";
            echo "<td>{$tx['chat_id']}</td>";
            echo "<td>{$tx['amount']} USD</td>";
            echo "<td>{$tx['status']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td style='color: $age_color;'>" . round($age_minutes, 1) . " min</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <p style="background: #e8f5e8; padding: 10px; border-left: 4px solid green;">
        <strong>🤖 Auto QR Cleanup Active!</strong><br>
        This page automatically checks for expired QR codes every 60 seconds.<br>
        Keep this page open in a browser tab to maintain automatic cleanup.
    </p>
    
    <p><strong>How it works:</strong></p>
    <ul>
        <li>⏰ Checks every 60 seconds for QR codes older than 3 minutes</li>
        <li>🗑️ Deletes expired QR messages from Telegram</li>
        <li>📨 Sends expiration notification to user</li>
        <li>📊 Updates database status to 'expired'</li>
    </ul>
    
    <script>
        // Add a visual indicator that the page is auto-refreshing
        let countdown = 60;
        const timer = setInterval(() => {
            countdown--;
            document.title = `Auto QR Cleanup (${countdown}s)`;
            if (countdown <= 0) {
                document.title = 'Auto QR Cleanup';
                clearInterval(timer);
            }
        }, 1000);
    </script>
</body>
</html>
