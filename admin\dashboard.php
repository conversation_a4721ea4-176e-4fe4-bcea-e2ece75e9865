<?php
require_once '../config.php';

// Check if admin is logged in
if (!is_admin_logged_in()) {
    redirect('../index.php');
}

// Check session timeout
if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time']) > SESSION_TIMEOUT) {
    session_destroy();
    redirect('../index.php?timeout=1');
}

// Get statistics
try {
    $stats = [];
    
    // Total accounts
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM accounts");
    $stats['total_accounts'] = $stmt->fetchColumn();
    
    // Available accounts
    $stmt = $pdo->query("SELECT COUNT(*) as available FROM accounts WHERE status = 'available'");
    $stats['available_accounts'] = $stmt->fetchColumn();
    
    // Sold accounts
    $stmt = $pdo->query("SELECT COUNT(*) as sold FROM accounts WHERE status = 'sold'");
    $stats['sold_accounts'] = $stmt->fetchColumn();
    
    // Total revenue
    $stmt = $pdo->query("SELECT COALESCE(SUM(amount), 0) as revenue FROM purchases WHERE payment_status = 'completed'");
    $stats['total_revenue'] = $stmt->fetchColumn();
    
    // Recent purchases
    $stmt = $pdo->query("
        SELECT p.*, a.account_code, a.game_type 
        FROM purchases p 
        LEFT JOIN accounts a ON p.account_id = a.id 
        ORDER BY p.created_at DESC 
        LIMIT 5
    ");
    $recent_purchases = $stmt->fetchAll();
    
    // Telegram users count
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM telegram_users");
    $stats['telegram_users'] = $stmt->fetchColumn();
    
} catch (PDOException $e) {
    $error_message = "Error loading dashboard data: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Account Management System</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --dark: #0f172a;
            --sidebar-bg: #1e293b;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border: #e5e7eb;
            --hover: #f3f4f6;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            z-index: 1000;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            transform: translateX(4px);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-left: auto;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .logout-btn {
            background: var(--danger);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .logout-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }
        
        /* Dashboard Content */
        .dashboard-content {
            padding: 2rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }
        
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .stat-change {
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .stat-change.positive {
            color: var(--success);
        }
        
        .stat-change.negative {
            color: var(--danger);
        }
        
        /* Quick Actions */
        .quick-actions {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1.5rem;
            color: var(--text-primary);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
            text-decoration: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }
        
        .action-btn i {
            font-size: 1.25rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link active">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="balance_management.php" class="nav-link">
                    <i class="fas fa-wallet"></i>
                    Balance Management
                </a>
            </div>
            <div class="nav-item">
                <a href="topup_settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Top-Up Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Dashboard</h1>
            <div class="user-menu">
                <div class="user-avatar">
                    <?php echo strtoupper(substr($_SESSION['admin_username'], 0, 1)); ?>
                </div>
                <span>Welcome, <?php echo htmlspecialchars($_SESSION['admin_username']); ?></span>
                <a href="logout.php" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i> Logout
                </a>
            </div>
        </div>
        
        <div class="dashboard-content">
            <!-- Stats Grid -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Accounts</div>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                            <i class="fas fa-gamepad"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo number_format($stats['total_accounts'] ?? 0); ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> Active inventory
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Available</div>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #10b981, #047857);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo number_format($stats['available_accounts'] ?? 0); ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> Ready for sale
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Sold Accounts</div>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo number_format($stats['sold_accounts'] ?? 0); ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> Completed sales
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Revenue</div>
                        <div class="stat-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                    <div class="stat-value">$<?php echo number_format($stats['total_revenue'] ?? 0, 2); ?></div>
                    <div class="stat-change positive">
                        <i class="fas fa-arrow-up"></i> Total earnings
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="quick-actions">
                <h2 class="section-title">Quick Actions</h2>
                <div class="actions-grid">
                    <a href="add_account.php" class="action-btn">
                        <i class="fas fa-plus-circle"></i>
                        Add New Account
                    </a>
                    <a href="accounts.php" class="action-btn">
                        <i class="fas fa-list"></i>
                        Manage Accounts
                    </a>
                    <a href="purchases.php" class="action-btn">
                        <i class="fas fa-shopping-cart"></i>
                        View Purchases
                    </a>
                    <a href="telegram_users.php" class="action-btn">
                        <i class="fab fa-telegram"></i>
                        Telegram Users
                    </a>
                    <a href="bot_webhook.php" class="action-btn">
                        <i class="fas fa-robot"></i>
                        Bot Settings
                    </a>
                    <a href="security_lockouts.php" class="action-btn">
                        <i class="fas fa-shield-alt"></i>
                        Security Center
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
