<?php
require_once 'config.php';

// Get parameters
$chat_id = $_POST['chat_id'] ?? null;
$md5 = $_POST['md5'] ?? null;
$delay = (int)($_POST['delay'] ?? 180); // Default 3 minutes

if (!$chat_id || !$md5) {
    error_log("QR deletion webhook: Missing parameters");
    exit;
}

error_log("QR deletion webhook started: chat_id=$chat_id, md5=$md5, delay={$delay}s");

// Wait for the specified delay
sleep($delay);

try {
    // Check if transaction is still pending
    $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
    $stmt->execute([$chat_id, $md5]);
    $transaction = $stmt->fetch();
    
    if (!$transaction) {
        error_log("QR deletion webhook: Transaction not found or already processed");
        exit;
    }
    
    error_log("QR deletion webhook: Processing expired QR for chat_id: $chat_id, md5: $md5");
    
    // Delete the QR message from Telegram
    if ($transaction['message_id']) {
        $bot_token = TELEGRAM_BOT_TOKEN;
        $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
        
        $data = [
            'chat_id' => $chat_id,
            'message_id' => $transaction['message_id']
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        error_log("QR deletion webhook: Delete API response - HTTP $http_code: $response");
        
        if ($http_code == 200) {
            error_log("QR deletion webhook: Message deleted successfully");
        } else {
            error_log("QR deletion webhook: Failed to delete message");
            
            // Parse error response
            $response_data = json_decode($response, true);
            if ($response_data && isset($response_data['description'])) {
                error_log("QR deletion webhook: Error - " . $response_data['description']);
            }
        }
    }
    
    // Update transaction status to expired
    $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE chat_id = ? AND md5 = ?");
    $stmt->execute([$chat_id, $md5]);
    
    // Remove from payment monitoring
    $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
    $stmt->execute([$chat_id, $md5]);
    
    // Send expiration message
    $bot_token = TELEGRAM_BOT_TOKEN;
    $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    
    $data = [
        'chat_id' => $chat_id,
        'text' => "⏰ QR code expired. Please generate a new one if needed.",
        'parse_mode' => 'HTML'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    error_log("QR deletion webhook: Expiration message sent");
    error_log("QR deletion webhook: Process completed for chat_id: $chat_id, md5: $md5");
    
} catch (Exception $e) {
    error_log("QR deletion webhook error: " . $e->getMessage());
}
?>
