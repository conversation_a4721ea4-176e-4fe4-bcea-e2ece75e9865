<?php
/**
 * Background Payment Checking Script
 * 
 * This script runs in the background and checks payment status every 10 seconds
 * Like your Python auto_check_transaction function
 * Usage: php check_payment_background.php <chat_id> <md5> <amount>
 */

require_once 'config.php';
require_once 'telegram_bot.php';

// Get parameters from command line
$chat_id = $argv[1] ?? null;
$md5 = $argv[2] ?? null;
$amount = $argv[3] ?? null;

if (!$chat_id || !$md5 || !$amount) {
    error_log("Background payment checking: Missing parameters");
    exit(1);
}

error_log("Background payment checking started for chat_id: $chat_id, md5: $md5, amount: $amount");

$check_count = 0;
$max_checks = 180; // Check for 3 minutes (180 * 1 second = 180 seconds)

while ($check_count < $max_checks) {
    try {
        // Check if transaction still exists and is pending
        $stmt = $pdo->prepare("SELECT status FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        $transaction = $stmt->fetch();
        
        if (!$transaction || $transaction['status'] !== 'pending') {
            error_log("Background payment checking: Transaction no longer pending, stopping checks");
            break;
        }
        
        // Check payment status via Bakong API
        $api_url = "https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5";
        $payload = json_encode(['md5' => $md5]);
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code == 200 || $http_code == 201) {
            $response_data = json_decode($response, true);
            
            if (isset($response_data['responseCode']) && $response_data['responseCode'] == 0) {
                // Payment successful! But check if already processed to prevent duplicates
                error_log("Background payment checking: Payment detected! Checking if already processed...");

                // CRITICAL: Check if this MD5 was already processed (prevent duplicate balance updates)
                $stmt = $pdo->prepare("SELECT status FROM bot_transactions WHERE md5 = ? AND status = 'completed'");
                $stmt->execute([$md5]);
                $already_processed = $stmt->fetch();

                if ($already_processed) {
                    error_log("Background payment checking: MD5 $md5 already processed, skipping to prevent duplicate balance update");
                    break;
                }

                // Mark as completed FIRST to prevent race conditions
                $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
                $affected_rows = $stmt->execute([$chat_id, $md5]);

                if ($stmt->rowCount() == 0) {
                    error_log("Background payment checking: Transaction already processed by another process, skipping");
                    break;
                }

                $bot = new TelegramBot(TELEGRAM_BOT_TOKEN, $pdo);

                // Send success message
                $bot->sendMessage($chat_id, "Payment of {$amount} USD confirmed! ✅");

                // Delete QR code message
                $stmt = $pdo->prepare("SELECT message_id FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
                $stmt->execute([$chat_id, $md5]);
                $transaction = $stmt->fetch();

                if ($transaction && $transaction['message_id']) {
                    try {
                        $bot->deleteMessage($chat_id, $transaction['message_id']);
                    } catch (Exception $e) {
                        error_log("Background payment checking: Failed to delete message - " . $e->getMessage());
                    }
                }

                // Update user balance (ONLY ONCE per MD5)
                $stmt = $pdo->prepare("SELECT id FROM telegram_users WHERE telegram_id = ?");
                $stmt->execute([$chat_id]);
                $user = $stmt->fetch();

                if ($user) {
                    $pdo->beginTransaction();

                    // Check if balance transaction already exists for this MD5
                    $stmt = $pdo->prepare("SELECT id FROM balance_transactions WHERE description LIKE ? AND telegram_user_id = ?");
                    $stmt->execute(["%$md5%", $user['id']]);
                    $existing_balance_tx = $stmt->fetch();

                    if (!$existing_balance_tx) {
                        // Update balance
                        $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
                        $stmt->execute([$amount, $user['id']]);

                        // Record transaction with MD5 in description for tracking
                        $stmt = $pdo->prepare("
                            INSERT INTO balance_transactions
                            (telegram_user_id, transaction_type, amount, description)
                            VALUES (?, 'topup', ?, ?)
                        ");
                        $stmt->execute([$user['id'], $amount, "Top-up via KHQR API - MD5: $md5"]);

                        $pdo->commit();

                        error_log("Background payment checking: Balance updated successfully for user {$user['id']}, MD5: $md5");
                    } else {
                        $pdo->rollBack();
                        error_log("Background payment checking: Balance already updated for MD5: $md5, skipping duplicate");
                    }
                } else {
                    error_log("Background payment checking: User not found for chat_id: $chat_id");
                }

                // Remove from monitoring
                $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
                $stmt->execute([$chat_id, $md5]);

                error_log("Background payment checking: Payment processing completed for MD5: $md5");
                break; // Exit the loop
            }
        }
        
        $check_count++;
        error_log("Background payment checking: Check $check_count/$max_checks - Payment not found yet");
        
        // Wait 1 second before next check (faster checking)
        sleep(1);
        
    } catch (Exception $e) {
        error_log("Background payment checking error: " . $e->getMessage());
        sleep(1); // Wait before retrying
        $check_count++;
    }
}

error_log("Background payment checking finished for chat_id: $chat_id, md5: $md5");
?>
