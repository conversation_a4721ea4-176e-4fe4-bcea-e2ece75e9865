<?php
// Manual table creation script
require_once 'config.php';

echo "<h2>Creating Missing Tables</h2>";

try {
    // Check if tables exist first
    $tables_to_check = ['bot_transactions', 'payment_monitoring', 'qr_deletion_schedule'];
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<p>✅ Table '$table' already exists</p>";
        } else {
            echo "<p>❌ Table '$table' does not exist - creating...</p>";
            
            if ($table === 'bot_transactions') {
                $sql = "CREATE TABLE bot_transactions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    chat_id BIGINT NOT NULL,
                    md5 VARCHAR(32) NOT NULL,
                    message_id INT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status ENUM('pending', 'completed', 'expired', 'failed') DEFAULT 'pending',
                    UNIQUE KEY unique_chat_md5 (chat_id, md5),
                    INDEX idx_chat_id (chat_id),
                    INDEX idx_md5 (md5),
                    INDEX idx_status (status)
                )";
                $pdo->exec($sql);
                echo "<p>✅ Created bot_transactions table</p>";
            }
            
            if ($table === 'payment_monitoring') {
                $sql = "CREATE TABLE payment_monitoring (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    chat_id BIGINT NOT NULL,
                    md5 VARCHAR(32) NOT NULL,
                    amount DECIMAL(10,2) NOT NULL,
                    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    check_count INT DEFAULT 0,
                    max_checks INT DEFAULT 18,
                    last_checked_at TIMESTAMP NULL,
                    status ENUM('active', 'completed', 'expired') DEFAULT 'active',
                    UNIQUE KEY unique_chat_md5 (chat_id, md5),
                    INDEX idx_chat_id (chat_id),
                    INDEX idx_status (status),
                    INDEX idx_started_at (started_at)
                )";
                $pdo->exec($sql);
                echo "<p>✅ Created payment_monitoring table</p>";
            }
            
            if ($table === 'qr_deletion_schedule') {
                $sql = "CREATE TABLE qr_deletion_schedule (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    chat_id BIGINT NOT NULL,
                    md5 VARCHAR(32) NOT NULL,
                    delete_at TIMESTAMP NOT NULL,
                    processed TINYINT(1) DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_chat_md5 (chat_id, md5),
                    INDEX idx_delete_at (delete_at),
                    INDEX idx_processed (processed)
                )";
                $pdo->exec($sql);
                echo "<p>✅ Created qr_deletion_schedule table</p>";
            }
        }
    }
    
    echo "<h3>🎉 All tables are ready!</h3>";
    echo "<p>You can now test the bot again.</p>";
    
    // Test the tables
    echo "<h3>Testing Tables:</h3>";
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
        $count = $stmt->fetchColumn();
        echo "<p>📊 Table '$table': $count records</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
