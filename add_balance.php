<?php
require_once 'config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    redirect('login.php');
}

// Force fresh data when coming from successful payment
$forceRefresh = false;
if (isset($_GET['payment_success']) || strpos($_SERVER['HTTP_REFERER'] ?? '', 'successful.php') !== false) {
    $forceRefresh = true;
    // Clear any cached data
    if (isset($_SESSION['cached_deposit_data'])) {
        unset($_SESSION['cached_deposit_data']);
    }
}

// Check if there's an update flag file from the cron job
$updateFlagFile = "temp/payment_update_{$_SESSION['user_id']}.txt";
if (file_exists($updateFlagFile)) {
    $updateTime = (int)file_get_contents($updateFlagFile);
    $currentTime = time();
    // If the update flag is newer than 5 minutes, force a refresh
    if (($currentTime - $updateTime) < 300) {
        $forceRefresh = true;
        // Remove the file to prevent unnecessary refreshes
        @unlink($updateFlagFile);
    }
}

// Get database instance
$db = getDB();

try {
    // Fetch user details with balance
    $query = "SELECT username, email, balance FROM users WHERE id = ?";
    $user_results = $db->select($query, [$_SESSION['user_id']]);
    $user = $user_results[0] ?? null;

    // Get username
    $username = $user['username'] ?? $_SESSION['username'];

    // Fetch successful transactions from qrcode_data
    $deposit_query = "
        SELECT 
            COUNT(*) as deposit_count,
            COALESCE(SUM(amount), 0) as total_deposits
        FROM 
            qrcode_data
        WHERE 
            username = ? AND status = 'Successful'
    ";
    $deposit_results = $db->select($deposit_query, [$username]);
    
    // Add deposit data to user array
    if (!empty($deposit_results)) {
        $user['deposit_count'] = $deposit_results[0]['deposit_count'];
        $user['total_deposits'] = $deposit_results[0]['total_deposits'];
    } else {
        $user['deposit_count'] = 0;
        $user['total_deposits'] = 0;
    }

    // Fetch recent deposit transactions
    $deposits_query = "
        SELECT 
            amount, 
            created_at,
            'deposit' as transaction_type
        FROM 
            qrcode_data
        WHERE 
            username = ? AND status = 'Successful'
        ORDER BY 
            created_at DESC
        LIMIT 3
    ";
    $recent_deposits = $db->select($deposits_query, [$username]);

} catch (Exception $e) {
    // Log the error
    error_log("Add Balance Page Error: " . $e->getMessage());
    
    // Show a user-friendly error message
    $_SESSION['error'] = "An error occurred while loading the page. Please try again.";
    redirect('dashboard.php');
    exit();
}

// Initialize variables
$error = '';
$success = '';

// Process balance addition
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Invalid request, please try again.';
    } else {
        // Sanitize and validate input
        $amount = filter_input(INPUT_POST, 'amount', FILTER_VALIDATE_FLOAT);

        // Validate amount
        if ($amount === false || $amount < 0.01) {
            $error = 'Please enter a valid amount of at least $0.01.';
        } else {
            // Get database instance
            $db = getDB();
            $user_id = $_SESSION['user_id'];

            try {
                // Start a transaction-like process
                // First, update user balance
                $update_balance_query = "UPDATE users SET balance = balance + ? WHERE id = ?";
                $balance_result = $db->update($update_balance_query, [$amount, $user_id]);

                if ($balance_result > 0) {
                    // Add a manual entry to qrcode_data as Successful
                    $qr_data = 'manual_addition_' . time();
                    $md5 = md5($qr_data . $user_id . $amount);
                    $insert_qrcode_query = "
                        INSERT INTO qrcode_data 
                        (qr_data, amount, md5, username, status) 
                        VALUES (?, ?, ?, ?, 'Successful')
                    ";
                    $insert_result = $db->insert($insert_qrcode_query, [
                        $qr_data, 
                        $amount, 
                        $md5, 
                        $_SESSION['username']
                    ]);

                    if ($insert_result) {
                        // Set success message
                        $success = "Successfully added $" . number_format($amount, 2) . " to your balance.";

                        // Fetch updated user balance
                        $balance_query = "SELECT balance FROM users WHERE id = ?";
                        $balance_result = $db->select($balance_query, [$user_id]);
                        
                        if (!empty($balance_result)) {
                            // Update session balance
                            $_SESSION['balance'] = $balance_result[0]['balance'];
                        }
                    } else {
                        // If transaction recording fails, attempt to rollback balance update
                        $rollback_query = "UPDATE users SET balance = balance - ? WHERE id = ?";
                        $db->update($rollback_query, [$amount, $user_id]);
                        
                        $error = "Failed to record transaction. Please try again.";
                    }
                } else {
                    $error = "Failed to update balance. Please try again.";
                }
            } catch (Exception $e) {
                // Log the error
                error_log("Balance addition error: " . $e->getMessage());
                $error = "An error occurred: " . $e->getMessage();
            }
        }
    }
}

// Generate CSRF token
$csrf_token = generateCSRFToken();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Balance - CamboKey</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --primary-color: #0077b6;
            --secondary-color: #90e0ef;
            --bg-dark: #0a192f;
            --text-light: rgba(255, 255, 255, 0.8);
            --white: #ffffff;
            --card-bg: rgba(255, 255, 255, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background: linear-gradient(135deg, var(--bg-dark), var(--primary-color), var(--secondary-color));
            color: var(--white);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .add-balance-wrapper {
            flex-grow: 1;
            padding: 40px 20px;
        }

        .add-balance-container {
            max-width: 1400px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
        }

        .balance-info-section {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
            height: fit-content;
        }

        .balance-methods-section {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .balance-card {
            background: var(--card-bg);
            border-radius: 15px;
            padding: 30px;
            backdrop-filter: blur(15px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        .balance-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .balance-title {
            color: var(--secondary-color);
            font-size: 1.5rem;
            font-weight: bold;
        }

        .current-balance {
            font-size: 2rem;
            color: var(--secondary-color);
            font-weight: bold;
        }

        .balance-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .balance-stat {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            color: var(--secondary-color);
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .payment-method {
            display: flex;
            align-items: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: transform 0.3s ease;
        }

        .payment-method:hover {
            transform: scale(1.02);
        }

        .payment-icon {
            font-size: 2rem;
            margin-right: 15px;
            color: var(--secondary-color);
        }

        .payment-details {
            flex-grow: 1;
        }

        .payment-name {
            font-weight: bold;
            color: var(--white);
            margin-bottom: 5px;
        }

        .payment-description {
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .payment-btn {
            background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .payment-btn:hover {
            transform: scale(1.05);
        }

        .recent-deposits {
            margin-top: 20px;
        }

        .deposit-list {
            list-style-type: none;
        }

        .deposit-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
        }

        .deposit-amount {
            font-weight: bold;
            color: var(--secondary-color);
        }

        .deposit-date {
            color: var(--text-light);
            font-size: 0.8rem;
        }

        .khqr-section {
            text-align: center;
            margin-top: 20px;
        }

        .khqr-image {
            max-width: 250px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 1024px) {
            .add-balance-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <?php includeHeader(); ?>

    <div class="add-balance-wrapper">
        <div class="add-balance-container">
            <!-- Balance Information Section -->
            <div class="balance-info-section">
                <div class="balance-header">
                    <span class="balance-title">My Wallet</span>
                    <span class="current-balance">$<?php echo number_format($user['balance'], 2); ?></span>
                </div>

                <div class="balance-stats">
                    <div class="balance-stat">
                        <div class="stat-value"><?php echo $user['deposit_count']; ?></div>
                        <div class="stat-label">Total Deposits</div>
                    </div>
                    <div class="balance-stat">
                        <div class="stat-value">$<?php echo number_format($user['total_deposits'], 2); ?></div>
                        <div class="stat-label">Total Added</div>
                    </div>
                </div>

                <div class="recent-deposits">
                    <h3 class="balance-title">Recent Deposits</h3>
                    <ul class="deposit-list">
                        <?php if (empty($recent_deposits)): ?>
                            <li class="deposit-item">No recent deposits</li>
                        <?php else: ?>
                            <?php foreach ($recent_deposits as $deposit): ?>
                                <li class="deposit-item">
                                    <span class="deposit-amount">$<?php echo number_format($deposit['amount'], 2); ?></span>
                                    <span class="deposit-date">
                                        <?php 
                                        // Convert to Cambodia timezone by subtracting 8 hours and adding 1 day
                                        $timestamp = strtotime($deposit['created_at']) - (8 * 3600); // Subtract 8 hours
                                        $timestamp = $timestamp + (24 * 3600); // Add 1 day (24 hours)
                                        
                                        $hours = date('g', $timestamp);
                                        $minutes = date('i', $timestamp);
                                        $ampm = date('a', $timestamp);
                                        
                                        // Reverse AM/PM as requested
                                        if ($ampm == 'am') {
                                            $ampm = 'PM';
                                        } else {
                                            $ampm = 'AM';
                                        }
                                        
                                        echo date('M j, Y', $timestamp) . ' ' . $hours . ':' . $minutes . ' ' . $ampm;
                                        ?>
                                    </span>
                                </li>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </ul>
                </div>
            </div>

            <!-- Balance Methods Section -->
            <div class="balance-methods-section">
                <!-- KHQR Payment Method -->
                <div class="balance-card">
                    <div class="balance-header">
                        <span class="balance-title">KHQR Payment</span>
                        <i class="fas fa-qrcode payment-icon"></i>
                    </div>
                    
                    <form action="request.php" method="GET" class="payment-form">
                        <input type="hidden" name="username" value="<?php echo htmlspecialchars($_SESSION['username']); ?>">
                        
                        <div class="form-group">
                            <label for="amount" style="color: var(--text-light); margin-bottom: 10px; display: block;">
                                Enter Amount to Add (USD)
                            </label>
                            <input 
                                type="number" 
                                id="amount" 
                                name="amount" 
                                min="0.01" 
                                max="1000" 
                                step="0.01" 
                                placeholder="Minimum $0.01, Maximum $1000" 
                                required
                                style="
                                    width: 100%;
                                    padding: 12px;
                                    background: rgba(0, 0, 0, 0.2);
                                    border: none;
                                    border-radius: 8px;
                                    color: var(--white);
                                    margin-bottom: 15px;
                                "
                            >
                        </div>

                            <p style="color: var(--text-light); margin-top: 10px;">
                                Scan the QR code or enter amount to proceed
                            </p>
                        </div>

                        <button type="submit" class="payment-btn" style="width: 100%; margin-top: 15px; padding: 12px 0; border-radius: 8px; background: linear-gradient(to right, var(--primary-color), var(--secondary-color)); border: none; color: white; cursor: pointer; font-weight: bold;">
                            Generate KHQR Payment
                        </button>
                    </form>
                </div>

                <!-- Other Payment Methods -->
                <div class="balance-card">
                    <div class="balance-header">
                        <span class="balance-title">Other Payment Methods</span>
                        <i class="fas fa-credit-card payment-icon"></i>
                    </div>

                    <div class="payment-methods">
                        <div class="payment-method">
                            <i class="fab fa-paypal payment-icon"></i>
                            <div class="payment-details">
                                <div class="payment-name">PayPal</div>
                                <div class="payment-description">Quick and secure online payments</div>
                            </div>
                            <button class="payment-btn" disabled>Coming Soon</button>
                        </div>

                        <div class="payment-method" style="margin-top: 15px;">
                            <i class="fab fa-stripe payment-icon"></i>
                            <div class="payment-details">
                                <div class="payment-name">Credit/Debit Card</div>
                                <div class="payment-description">Instant bank card transactions</div>
                            </div>
                            <button class="payment-btn" disabled>Coming Soon</button>
                        </div>
                    </div>

                    <div class="payment-info" style="
                        background: rgba(0, 0, 0, 0.2);
                        border-radius: 10px;
                        padding: 15px;
                        margin-top: 20px;
                        color: var(--text-light);
                        font-size: 0.9rem;
                    ">
                        <h4 style="color: var(--secondary-color); margin-bottom: 10px;">Payment Method Guidelines</h4>
                        <ul style="list-style-type: disc; padding-left: 20px;">
                            <li>Minimum deposit: $0.01</li>
                            <li>Maximum deposit: $1000</li>
                            <li>Instant balance update after successful payment</li>
                            <li>Secure and encrypted transactions</li>
                        </ul>
                    </div>
                </div>

                <!-- Balance Usage Information -->
                <div class="balance-card">
                    <div class="balance-header">
                        <span class="balance-title">Balance Usage</span>
                        <i class="fas fa-wallet payment-icon"></i>
                    </div>

                    <div class="balance-usage-info" style="
                        color: var(--text-light);
                        font-size: 0.9rem;
                    ">
                        <p>Your CamboKey wallet balance can be used to:</p>
                        <ul style="list-style-type: disc; padding-left: 20px; margin-bottom: 15px;">
                            <li>Purchase digital products</li>
                            <li>Access exclusive software</li>
                            <li>Make instant transactions</li>
                        </ul>

                        <div style="
                            background: rgba(0, 0, 0, 0.2);
                            border-radius: 10px;
                            padding: 15px;
                            margin-top: 15px;
                        ">
                            <h4 style="color: var(--secondary-color); margin-bottom: 10px;">Balance Benefits</h4>
                            <p>💡 Adding balance to your wallet provides:</p>
                            <ul style="list-style-type: disc; padding-left: 20px;">
                                <li>Faster checkout process</li>
                                <li>No need to enter payment details for each purchase</li>
                                <li>Potential future loyalty rewards</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php includeFooter(); ?>

    <script>
        // Validate amount input
        document.getElementById('amount').addEventListener('input', function() {
            const amount = parseFloat(this.value);
            if (amount < 0.01) {
                this.setCustomValidity('Minimum amount is $0.01');
            } else if (amount > 1000) {
                this.setCustomValidity('Maximum amount is $1000');
            } else {
                this.setCustomValidity('');
            }
        });

        // Copy to clipboard function for KHQR details
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Copied to clipboard!');
            }, function(err) {
                console.error('Could not copy text: ', err);
            });
        }
    </script>
</body>
</html> 
 