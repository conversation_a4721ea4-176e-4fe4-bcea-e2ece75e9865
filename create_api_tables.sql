-- Create tables for API-based QR code system

-- Table to store bot transactions (like Python transactions dict)
CREATE TABLE IF NOT EXISTS bot_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chat_id BIGINT NOT NULL,
    md5 VARCHAR(32) NOT NULL,
    message_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    status ENUM('pending', 'completed', 'expired', 'failed') DEFAULT 'pending',
    UNIQUE KEY unique_chat_md5 (chat_id, md5),
    INDEX idx_chat_id (chat_id),
    INDEX idx_md5 (md5),
    INDEX idx_status (status)
);

-- Table to store payment monitoring (like Python threading)
CREATE TABLE IF NOT EXISTS payment_monitoring (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chat_id BIGINT NOT NULL,
    md5 VARCHAR(32) NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    check_count INT DEFAULT 0,
    max_checks INT DEFAULT 18,
    last_checked_at TIMESTAMP NULL,
    status ENUM('active', 'completed', 'expired') DEFAULT 'active',
    UNIQUE KEY unique_chat_md5 (chat_id, md5),
    INDEX idx_chat_id (chat_id),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
);

-- Table to schedule QR code deletions (like Python delete_qr_after_timeout)
CREATE TABLE IF NOT EXISTS qr_deletion_schedule (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chat_id BIGINT NOT NULL,
    md5 VARCHAR(32) NOT NULL,
    delete_at TIMESTAMP NOT NULL,
    processed TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE KEY unique_chat_md5 (chat_id, md5),
    INDEX idx_delete_at (delete_at),
    INDEX idx_processed (processed)
);

-- Add indexes to existing tables for better performance
ALTER TABLE telegram_users ADD INDEX IF NOT EXISTS idx_telegram_id (telegram_id);
ALTER TABLE balance_transactions ADD INDEX IF NOT EXISTS idx_telegram_user_id (telegram_user_id);
ALTER TABLE balance_transactions ADD INDEX IF NOT EXISTS idx_transaction_type (transaction_type);
