<?php
require_once 'config.php';

echo "<h2>🗄️ Create QR Deletion Schedule Table</h2>";

try {
    // Create QR deletion schedule table
    $sql = "
    CREATE TABLE IF NOT EXISTS qr_deletion_schedule (
        id INT AUTO_INCREMENT PRIMARY KEY,
        chat_id BIGINT NOT NULL,
        md5 VARCHAR(32) NOT NULL,
        delete_at DATETIME NOT NULL,
        processed TINYINT(1) DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE KEY unique_qr (chat_id, md5),
        INDEX idx_delete_at (delete_at),
        INDEX idx_processed (processed)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $pdo->exec($sql);
    echo "<p style='color: green;'>✅ QR deletion schedule table created successfully!</p>";
    
    // Show table structure
    $stmt = $pdo->query("DESCRIBE qr_deletion_schedule");
    $columns = $stmt->fetchAll();
    
    echo "<h3>📋 Table Structure:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f0f0f0;'><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>🧪 Test Data:</h3>";
    
    // Show any existing scheduled deletions
    $stmt = $pdo->query("SELECT * FROM qr_deletion_schedule ORDER BY created_at DESC LIMIT 10");
    $schedules = $stmt->fetchAll();
    
    if ($schedules) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>Chat ID</th><th>MD5</th><th>Delete At</th><th>Processed</th><th>Created</th></tr>";
        
        foreach ($schedules as $schedule) {
            $processed_color = $schedule['processed'] ? 'green' : 'red';
            echo "<tr>";
            echo "<td>{$schedule['chat_id']}</td>";
            echo "<td>" . substr($schedule['md5'], 0, 8) . "...</td>";
            echo "<td>{$schedule['delete_at']}</td>";
            echo "<td style='color: $processed_color;'>" . ($schedule['processed'] ? 'Yes' : 'No') . "</td>";
            echo "<td>{$schedule['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No scheduled deletions found</p>";
    }
    
    echo "<h3>🎯 How It Works Now:</h3>";
    echo "<ol>";
    echo "<li><strong>QR Generated:</strong> Schedule deletion 3 minutes in future</li>";
    echo "<li><strong>User Activity:</strong> Every message checks for expired QRs</li>";
    echo "<li><strong>Auto Delete:</strong> Expired QRs deleted automatically</li>";
    echo "<li><strong>No Background Process:</strong> No need for exec() function</li>";
    echo "</ol>";
    
    echo "<p style='background: #e8f5e8; padding: 10px; border-left: 4px solid green;'>";
    echo "<strong>✅ Ready to Test!</strong><br>";
    echo "Generate a QR code and wait 3+ minutes. The next time you send any message, expired QRs will be automatically deleted.";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
