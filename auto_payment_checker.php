<?php
/**
 * Auto Payment Checker - Checks specific payment immediately
 * Runs for 3 minutes checking every 1 second
 */

require_once 'config.php';

$chat_id = $_GET['chat_id'] ?? null;
$md5 = $_GET['md5'] ?? null;
$amount = $_GET['amount'] ?? null;
$message_id = $_GET['message_id'] ?? null;

if (!$chat_id || !$md5 || !$amount) {
    exit('Missing parameters');
}

// Set longer execution time and ignore user abort
set_time_limit(300); // 5 minutes
ignore_user_abort(true);

error_log("Auto payment checker started for chat_id: $chat_id, md5: $md5");

// Check for 3 minutes (180 seconds)
$max_checks = 180;
$check_count = 0;

while ($check_count < $max_checks) {
    $check_count++;
    
    try {
        // Check if transaction is still pending
        $stmt = $pdo->prepare("SELECT status FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        $transaction = $stmt->fetch();
        
        if (!$transaction || $transaction['status'] !== 'pending') {
            error_log("Auto checker: Transaction no longer pending for md5: $md5");
            break;
        }
        
        // Check payment status via API
        $api_url = "https://api.kunchhunlichhean.org/khqr/check?md5=" . urlencode($md5);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code == 200) {
            $response_data = json_decode($response, true);
            
            if (isset($response_data['responseCode']) && $response_data['responseCode'] == 0) {
                // PAYMENT FOUND!
                error_log("Auto checker: PAYMENT DETECTED for md5: $md5, amount: $amount");
                
                // Mark as completed to prevent duplicates
                $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
                $stmt->execute([$chat_id, $md5]);
                
                if ($stmt->rowCount() > 0) {
                    // Update user balance
                    $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE chat_id = ?");
                    $stmt->execute([$amount, $chat_id]);
                    
                    // Send success message via Telegram API
                    $bot_token = TELEGRAM_BOT_TOKEN;
                    $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
                    
                    $data = [
                        'chat_id' => $chat_id,
                        'text' => "✅ Payment of {$amount} USD confirmed!\n💰 Balance updated automatically!",
                        'parse_mode' => 'HTML'
                    ];
                    
                    $ch = curl_init();
                    curl_setopt($ch, CURLOPT_URL, $api_url);
                    curl_setopt($ch, CURLOPT_POST, true);
                    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                    
                    curl_exec($ch);
                    curl_close($ch);
                    
                    // Delete QR message
                    if ($message_id) {
                        $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
                        $data = [
                            'chat_id' => $chat_id,
                            'message_id' => $message_id
                        ];
                        
                        $ch = curl_init();
                        curl_setopt($ch, CURLOPT_URL, $api_url);
                        curl_setopt($ch, CURLOPT_POST, true);
                        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                        
                        curl_exec($ch);
                        curl_close($ch);
                        
                        error_log("Auto checker: QR message deleted for md5: $md5");
                    }
                    
                    // Remove from payment monitoring
                    $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
                    $stmt->execute([$chat_id, $md5]);
                    
                    error_log("Auto checker: Payment processed successfully for chat_id: $chat_id, amount: $amount");
                    break; // Exit the loop
                }
            }
        }
        
        // Wait 1 second before next check
        sleep(1);
        
    } catch (Exception $e) {
        error_log("Auto checker error: " . $e->getMessage());
        sleep(1); // Wait before retrying
    }
}

error_log("Auto payment checker finished for md5: $md5");
?>
