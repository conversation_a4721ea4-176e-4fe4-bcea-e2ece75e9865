-- Database Schema for Account Management System
-- Run this SQL to create the required tables

CREATE DATABASE IF NOT EXISTS account_system;
USE account_system;

-- Admin users table
CREATE TABLE IF NOT EXISTS admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- Accounts table for storing account data
CREATE TABLE IF NOT EXISTS accounts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    account_code VARCHAR(50), -- Account ID/Code like "339633"
    account_data TEXT NOT NULL, -- Login details (username, password, etc.)
    account_status VARCHAR(50) DEFAULT 'Good', -- Status like "Good", "Fresh", etc.
    verify_code VARCHAR(50) DEFAULT 'Bug Code', -- Verify status
    inactive_days VARCHAR(50) DEFAULT '4Days+', -- Inactive period
    collector_status VARCHAR(50) DEFAULT 'Exalted', -- Collector status
    device_info VARCHAR(50) DEFAULT 'IDK', -- Device information
    additional_info TEXT, -- Any additional account information
    image_path VARCHAR(255),
    status ENUM('available', 'sold', 'reserved') DEFAULT 'available',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Telegram bot users table
CREATE TABLE IF NOT EXISTS telegram_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    telegram_id BIGINT UNIQUE NOT NULL,
    username VARCHAR(50),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    balance DECIMAL(10,2) DEFAULT 0.00,
    is_blocked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Purchases table
CREATE TABLE IF NOT EXISTS purchases (
    id INT AUTO_INCREMENT PRIMARY KEY,
    account_id INT NOT NULL,
    telegram_user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    qr_code_path VARCHAR(255),
    purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivery_status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
    account_file_path VARCHAR(255),
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE CASCADE,
    FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE CASCADE
);

-- Bot interactions log
CREATE TABLE IF NOT EXISTS bot_interactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    telegram_user_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE CASCADE
);

-- Balance transactions table
CREATE TABLE IF NOT EXISTS balance_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    telegram_user_id INT NOT NULL,
    transaction_type ENUM('topup', 'purchase', 'refund', 'admin_adjustment') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    description TEXT,
    reference_id VARCHAR(100), -- For linking to purchases or topup requests
    admin_id INT NULL, -- For admin adjustments
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE CASCADE
);

-- Top-up requests table
CREATE TABLE IF NOT EXISTS topup_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    telegram_user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'completed', 'failed', 'expired') DEFAULT 'pending',
    payment_method VARCHAR(50) DEFAULT 'qr_code',
    qr_code_path VARCHAR(255),
    transaction_id VARCHAR(100) UNIQUE,
    payment_reference VARCHAR(255),
    expires_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE CASCADE
);

-- Top-up settings table
CREATE TABLE IF NOT EXISTS topup_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_name VARCHAR(50) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- QR Code data table for payment tracking
CREATE TABLE IF NOT EXISTS qrcode_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    qr_data TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    md5 VARCHAR(255) NOT NULL,
    username VARCHAR(100) NOT NULL,
    status ENUM('Pending', 'Successful', 'Failed', 'Expired') DEFAULT 'Pending',
    telegram_user_id INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_md5 (md5),
    INDEX idx_username (username),
    INDEX idx_status (status)
);

-- Insert default admin user (password: admin123 - change this!)
INSERT INTO admin_users (username, password, email) VALUES
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '<EMAIL>');

-- Insert default top-up settings
INSERT INTO topup_settings (setting_name, setting_value, description) VALUES
('min_topup_amount', '10.00', 'Minimum amount for top-up'),
('max_topup_amount', '1000.00', 'Maximum amount for top-up'),
('topup_enabled', '1', 'Enable/disable top-up functionality'),
('qr_expiry_minutes', '30', 'QR code expiry time in minutes'),
('payment_gateway', 'manual', 'Payment gateway type (manual, auto)'),
('currency', 'USD', 'Currency for payments'),
('admin_topup_enabled', '1', 'Allow admin to add balance manually');

-- Create uploads directory structure (you'll need to create these folders manually)
-- uploads/
-- uploads/accounts/
-- uploads/qr_codes/
-- uploads/account_files/
