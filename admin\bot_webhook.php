<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$message = '';
$webhook_url = SITE_URL . '/telegram_bot.php';

// Handle webhook setup
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid security token.';
    } else {
        $action = $_POST['action'] ?? '';
        
        if ($action === 'set_webhook') {
            $result = setWebhook($webhook_url);
            $message = $result['message'];
        } elseif ($action === 'delete_webhook') {
            $result = deleteWebhook();
            $message = $result['message'];
        } elseif ($action === 'get_webhook_info') {
            $result = getWebhookInfo();
            $message = $result['message'];
        }
    }
}

function setWebhook($url) {
    $api_url = TELEGRAM_API_URL . 'setWebhook';
    $data = ['url' => $url];
    
    $options = [
        'http' => [
            'header' => "Content-type: application/x-www-form-urlencoded\r\n",
            'method' => 'POST',
            'content' => http_build_query($data)
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($api_url, false, $context);
    $response = json_decode($result, true);
    
    if ($response && $response['ok']) {
        return ['success' => true, 'message' => '✅ Webhook set successfully!'];
    } else {
        $error = $response['description'] ?? 'Unknown error';
        return ['success' => false, 'message' => '❌ Failed to set webhook: ' . $error];
    }
}

function deleteWebhook() {
    $api_url = TELEGRAM_API_URL . 'deleteWebhook';
    
    $options = [
        'http' => [
            'method' => 'POST'
        ]
    ];
    
    $context = stream_context_create($options);
    $result = file_get_contents($api_url, false, $context);
    $response = json_decode($result, true);
    
    if ($response && $response['ok']) {
        return ['success' => true, 'message' => '✅ Webhook deleted successfully!'];
    } else {
        $error = $response['description'] ?? 'Unknown error';
        return ['success' => false, 'message' => '❌ Failed to delete webhook: ' . $error];
    }
}

function getWebhookInfo() {
    $api_url = TELEGRAM_API_URL . 'getWebhookInfo';
    $result = file_get_contents($api_url);
    $response = json_decode($result, true);
    
    if ($response && $response['ok']) {
        $info = $response['result'];
        $status = $info['url'] ? 'Active' : 'Not Set';
        $pending = $info['pending_update_count'] ?? 0;
        $last_error = isset($info['last_error_date']) ? date('Y-m-d H:i:s', $info['last_error_date']) : 'None';
        
        return ['success' => true, 'message' => "ℹ️ Webhook Status: $status | Pending Updates: $pending | Last Error: $last_error"];
    } else {
        return ['success' => false, 'message' => '❌ Failed to get webhook info'];
    }
}

// Get current webhook info
$webhook_info = null;
try {
    $api_url = TELEGRAM_API_URL . 'getWebhookInfo';
    $result = file_get_contents($api_url);
    $response = json_decode($result, true);
    if ($response && $response['ok']) {
        $webhook_info = $response['result'];
    }
} catch (Exception $e) {
    // Ignore errors for display
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bot Settings - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --sidebar-bg: #1e293b;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border: #e5e7eb;
            --hover: #f3f4f6;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            transform: translateX(4px);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .bot-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
        }
        
        .status-active {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }
        
        /* Content */
        .content {
            padding: 2rem;
            width: 100%;
            max-width: none;
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
            border: 1px solid #93c5fd;
        }
        
        .settings-grid {
            display: grid;
            gap: 2rem;
        }
        
        .settings-card {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .card-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .card-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .info-item {
            padding: 1rem;
            background: var(--hover);
            border-radius: 12px;
            min-width: 0;
            overflow: hidden;
        }
        
        .info-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 0.5rem;
        }
        
        .info-value {
            font-weight: 600;
            color: var(--text-primary);
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 100%;
            cursor: pointer;
            position: relative;
        }

        .info-value:hover {
            overflow: visible;
            white-space: normal;
            background: var(--bg-secondary);
            padding: 0.5rem;
            border-radius: 8px;
            z-index: 10;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .url-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .copy-btn {
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 6px;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        }

        .info-item:hover .copy-btn {
            opacity: 1;
        }

        .copy-btn:hover {
            background: var(--primary-dark);
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .action-btn {
            padding: 1rem 1.5rem;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3);
        }
        
        .btn-danger {
            background: var(--danger);
            color: white;
        }
        
        .btn-danger:hover {
            background: #dc2626;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }
        
        .btn-secondary:hover {
            background: var(--text-primary);
            transform: translateY(-2px);
        }
        
        /* Responsive */
        @media (max-width: 1024px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .main-content {
                margin-left: 0;
            }

            .content {
                padding: 1rem;
            }

            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }

        @media (max-width: 768px) {
            .info-grid {
                grid-template-columns: 1fr;
            }

            .actions-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link active">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Bot Settings</h1>
            <div class="bot-status <?php echo ($webhook_info && $webhook_info['url']) ? 'status-active' : 'status-inactive'; ?>">
                <i class="fas fa-circle"></i>
                <?php echo ($webhook_info && $webhook_info['url']) ? 'Bot Active' : 'Bot Inactive'; ?>
            </div>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?php 
                    if (strpos($message, '✅') !== false) echo 'alert-success';
                    elseif (strpos($message, '❌') !== false) echo 'alert-error';
                    else echo 'alert-info';
                ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <div class="settings-grid">
                <!-- Webhook Configuration -->
                <div class="settings-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-link"></i>
                        </div>
                        <div>
                            <div class="card-title">Webhook Configuration</div>
                            <div class="card-description">Manage your Telegram bot webhook settings</div>
                        </div>
                    </div>
                    
                    <?php if ($webhook_info): ?>
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">Webhook URL</div>
                                <div class="info-value" title="<?php echo htmlspecialchars($webhook_info['url'] ?: 'Not Set'); ?>"><?php echo $webhook_info['url'] ?: 'Not Set'; ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Pending Updates</div>
                                <div class="info-value"><?php echo $webhook_info['pending_update_count'] ?? 0; ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Max Connections</div>
                                <div class="info-value"><?php echo $webhook_info['max_connections'] ?? 'Default'; ?></div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">Last Error</div>
                                <div class="info-value">
                                    <?php 
                                    if (isset($webhook_info['last_error_date'])) {
                                        echo date('M j, H:i', $webhook_info['last_error_date']);
                                    } else {
                                        echo 'None';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <div class="actions-grid">
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="set_webhook">
                            <button type="submit" class="action-btn btn-primary">
                                <i class="fas fa-play"></i>
                                Set Webhook
                            </button>
                        </form>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="delete_webhook">
                            <button type="submit" class="action-btn btn-danger">
                                <i class="fas fa-stop"></i>
                                Delete Webhook
                            </button>
                        </form>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <input type="hidden" name="action" value="get_webhook_info">
                            <button type="submit" class="action-btn btn-secondary">
                                <i class="fas fa-info-circle"></i>
                                Check Status
                            </button>
                        </form>
                    </div>
                </div>
                
                <!-- Bot Information -->
                <div class="settings-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="fas fa-robot"></i>
                        </div>
                        <div>
                            <div class="card-title">Bot Information</div>
                            <div class="card-description">Current bot configuration and URLs</div>
                        </div>
                    </div>
                    
                    <div class="info-grid">
                        <div class="info-item">
                            <div class="info-label">Bot Token</div>
                            <div class="info-value"><?php echo substr(TELEGRAM_BOT_TOKEN, 0, 10) . '...'; ?></div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Webhook URL</div>
                            <div class="url-container">
                                <div class="info-value" title="<?php echo htmlspecialchars($webhook_url); ?>"><?php echo htmlspecialchars($webhook_url); ?></div>
                                <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars($webhook_url); ?>')">Copy</button>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">Site URL</div>
                            <div class="url-container">
                                <div class="info-value" title="<?php echo htmlspecialchars(SITE_URL); ?>"><?php echo htmlspecialchars(SITE_URL); ?></div>
                                <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars(SITE_URL); ?>')">Copy</button>
                            </div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">API URL</div>
                            <div class="url-container">
                                <div class="info-value" title="<?php echo htmlspecialchars(TELEGRAM_API_URL); ?>"><?php echo htmlspecialchars(TELEGRAM_API_URL); ?></div>
                                <button class="copy-btn" onclick="copyToClipboard('<?php echo htmlspecialchars(TELEGRAM_API_URL); ?>')">Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                // Show a temporary success message
                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = '#10b981';

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '';
                }, 2000);
            }).catch(function(err) {
                console.error('Failed to copy: ', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                const btn = event.target;
                const originalText = btn.textContent;
                btn.textContent = 'Copied!';
                btn.style.background = '#10b981';

                setTimeout(() => {
                    btn.textContent = originalText;
                    btn.style.background = '';
                }, 2000);
            });
        }
    </script>
</body>
</html>
