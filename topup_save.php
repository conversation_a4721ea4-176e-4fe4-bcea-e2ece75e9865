<?php
require_once 'config.php';

// Set JSON response header
header('Content-Type: application/json');

// Function to send JSON response
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST requests are allowed');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, 'Invalid JSON input');
}

// Validate required fields
$required_fields = ['transaction_id', 'payment_reference'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty($input[$field])) {
        sendResponse(false, "Missing required field: $field");
    }
}

$transaction_id = $input['transaction_id'];
$payment_reference = $input['payment_reference'];
$admin_confirmed = isset($input['admin_confirmed']) ? (bool)$input['admin_confirmed'] : false;

try {
    $pdo->beginTransaction();
    
    // Get the top-up request
    $stmt = $pdo->prepare("
        SELECT tr.*, tu.telegram_id, tu.balance 
        FROM topup_requests tr
        JOIN telegram_users tu ON tr.telegram_user_id = tu.id
        WHERE tr.transaction_id = ?
    ");
    $stmt->execute([$transaction_id]);
    $request = $stmt->fetch();
    
    if (!$request) {
        $pdo->rollBack();
        sendResponse(false, 'Top-up request not found');
    }
    
    // Check if already completed
    if ($request['status'] === 'completed') {
        $pdo->rollBack();
        sendResponse(false, 'This top-up request has already been completed');
    }
    
    // Check if expired
    if ($request['status'] === 'expired' || strtotime($request['expires_at']) < time()) {
        $pdo->rollBack();
        sendResponse(false, 'This top-up request has expired');
    }
    
    // Check if failed or cancelled
    if ($request['status'] === 'failed') {
        $pdo->rollBack();
        sendResponse(false, 'This top-up request has been cancelled');
    }
    
    // For manual confirmation, we'll mark as completed
    // In a real system, you'd verify the payment with your payment gateway
    
    $current_balance = $request['balance'];
    $new_balance = $current_balance + $request['amount'];
    
    // Update user balance
    $stmt = $pdo->prepare("UPDATE telegram_users SET balance = ? WHERE id = ?");
    $stmt->execute([$new_balance, $request['telegram_user_id']]);
    
    // Update top-up request status
    $stmt = $pdo->prepare("
        UPDATE topup_requests 
        SET status = 'completed', payment_reference = ?, completed_at = NOW() 
        WHERE id = ?
    ");
    $stmt->execute([$payment_reference, $request['id']]);
    
    // Record balance transaction
    $stmt = $pdo->prepare("
        INSERT INTO balance_transactions 
        (telegram_user_id, transaction_type, amount, balance_before, balance_after, description, reference_id) 
        VALUES (?, 'topup', ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $request['telegram_user_id'],
        $request['amount'],
        $current_balance,
        $new_balance,
        'Top-up via ' . ($admin_confirmed ? 'admin confirmation' : 'payment gateway'),
        $transaction_id
    ]);
    
    $pdo->commit();
    
    sendResponse(true, 'Top-up completed successfully', [
        'transaction_id' => $transaction_id,
        'amount' => $request['amount'],
        'old_balance' => $current_balance,
        'new_balance' => $new_balance,
        'telegram_id' => $request['telegram_id']
    ]);
    
} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Top-up save error: " . $e->getMessage());
    sendResponse(false, 'Database error occurred');
} catch (Exception $e) {
    $pdo->rollBack();
    error_log("Top-up save error: " . $e->getMessage());
    sendResponse(false, 'An error occurred while processing the payment');
}
?>
