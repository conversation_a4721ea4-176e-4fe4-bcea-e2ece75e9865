<?php
/**
 * Auto Payment Checker - Runs immediately after QR is sent
 * Checks payment status every 2 seconds for 3 minutes
 * Usage: php auto_check_payment.php <chat_id> <md5> <amount>
 */

require_once 'config.php';
require_once 'telegram_bot.php';

// Get parameters
$chat_id = $argv[1] ?? null;
$md5 = $argv[2] ?? null;
$amount = $argv[3] ?? null;

if (!$chat_id || !$md5 || !$amount) {
    error_log("Auto payment check: Missing parameters");
    exit(1);
}

error_log("Auto payment check started: chat_id=$chat_id, md5=$md5, amount=$amount");

$check_count = 0;
$max_checks = 90; // 3 minutes (90 * 2 seconds = 180 seconds)

while ($check_count < $max_checks) {
    try {
        // Check if transaction still exists and is pending
        $stmt = $pdo->prepare("SELECT status FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        $transaction = $stmt->fetch();
        
        if (!$transaction || $transaction['status'] !== 'pending') {
            error_log("Auto payment check: Transaction no longer pending, stopping");
            break;
        }
        
        // Check payment via API
        $api_url = "https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5";
        $payload = json_encode(['md5' => $md5]);
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7ImlkIjoiYzlhZDQ1MDAwODNkNDNjNiJ9LCJpYXQiOjE3NTE2OTA3MDMsImV4cCI6MTc1OTQ2NjcwM30.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $payload);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($http_code == 200 || $http_code == 201) {
            $response_data = json_decode($response, true);
            
            if (isset($response_data['responseCode']) && $response_data['responseCode'] == 0) {
                // PAYMENT FOUND!
                error_log("Auto payment check: PAYMENT DETECTED! Processing...");
                
                // Mark as completed to prevent duplicates
                $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
                $stmt->execute([$chat_id, $md5]);
                
                if ($stmt->rowCount() == 0) {
                    error_log("Auto payment check: Already processed by another process");
                    break;
                }
                
                $bot = new TelegramBot(TELEGRAM_BOT_TOKEN, $pdo);
                
                // Send success message via direct API call
                sendDirectMessage($chat_id, "✅ Payment of {$amount} USD confirmed!\n💰 Balance updated automatically!");
                
                // Delete QR message
                $stmt = $pdo->prepare("SELECT message_id FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
                $stmt->execute([$chat_id, $md5]);
                $tx = $stmt->fetch();
                
                if ($tx && $tx['message_id']) {
                    try {
                        $bot->deleteMessage($chat_id, $tx['message_id']);
                    } catch (Exception $e) {
                        error_log("Auto payment check: Failed to delete message");
                    }
                }
                
                // Update balance directly
                updateUserBalanceDirectly($chat_id, $amount, $md5);
                
                // Clean up monitoring
                $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
                $stmt->execute([$chat_id, $md5]);
                
                error_log("Auto payment check: SUCCESS! Payment processed automatically");
                break;
            }
        }
        
        $check_count++;
        if ($check_count % 15 == 0) { // Log every 30 seconds
            error_log("Auto payment check: Check $check_count/$max_checks - Still waiting...");
        }
        
        // Wait 2 seconds before next check
        sleep(2);
        
    } catch (Exception $e) {
        error_log("Auto payment check error: " . $e->getMessage());
        sleep(2);
        $check_count++;
    }
}

if ($check_count >= $max_checks) {
    error_log("Auto payment check: Timeout reached, stopping checks");
}

error_log("Auto payment check finished for chat_id: $chat_id, md5: $md5");

// Helper function (copied from main bot)
function updateUserBalanceDirectly($chat_id, $amount, $md5) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
        $stmt->execute([$chat_id]);
        $user = $stmt->fetch();
        
        if (!$user) return;
        
        // Check if already processed
        $stmt = $pdo->prepare("SELECT id FROM balance_transactions WHERE description LIKE ? AND telegram_user_id = ?");
        $stmt->execute(["%$md5%", $user['id']]);
        if ($stmt->fetch()) return;
        
        // Update balance
        $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
        $stmt->execute([$amount, $user['id']]);
        
        // Record transaction
        $stmt = $pdo->prepare("INSERT INTO balance_transactions (telegram_user_id, transaction_type, amount, description) VALUES (?, 'topup', ?, ?)");
        $stmt->execute([$user['id'], $amount, "Auto KHQR Payment - MD5: $md5"]);
        
        error_log("Auto payment check: Balance updated successfully");
        
    } catch (Exception $e) {
        error_log("Auto payment check: Balance update error - " . $e->getMessage());
    }
}

// Direct API call function to avoid webhook loops
function sendDirectMessage($chat_id, $text) {
    $bot_token = TELEGRAM_BOT_TOKEN;
    $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    curl_close($ch);

    error_log("Direct message sent: $text");
}
?>
