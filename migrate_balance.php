<?php
require_once 'config.php';

echo "<h2>🔄 Balance System Migration</h2>";

try {
    // Add balance column to telegram_users if it doesn't exist
    $stmt = $pdo->query("SHOW COLUMNS FROM telegram_users LIKE 'balance'");
    if ($stmt->rowCount() == 0) {
        $pdo->exec("ALTER TABLE telegram_users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 AFTER last_name");
        echo "✅ Added balance column to telegram_users<br>\n";
    } else {
        echo "ℹ️ balance column already exists in telegram_users<br>\n";
    }
    
    // Create balance_transactions table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS balance_transactions (
            id INT AUTO_INCREMENT PRIMARY KEY,
            telegram_user_id INT NOT NULL,
            transaction_type ENUM('topup', 'purchase', 'refund', 'admin_adjustment') NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            balance_before DECIMAL(10,2) NOT NULL,
            balance_after DECIMAL(10,2) NOT NULL,
            description TEXT,
            reference_id VARCHAR(100),
            admin_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE CASCADE
        )
    ");
    echo "✅ Created balance_transactions table<br>\n";
    
    // Create topup_requests table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS topup_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            telegram_user_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            status ENUM('pending', 'completed', 'failed', 'expired') DEFAULT 'pending',
            payment_method VARCHAR(50) DEFAULT 'qr_code',
            qr_code_path VARCHAR(255),
            transaction_id VARCHAR(100) UNIQUE,
            payment_reference VARCHAR(255),
            expires_at TIMESTAMP NULL,
            completed_at TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (telegram_user_id) REFERENCES telegram_users(id) ON DELETE CASCADE
        )
    ");
    echo "✅ Created topup_requests table<br>\n";
    
    // Create topup_settings table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS topup_settings (
            id INT AUTO_INCREMENT PRIMARY KEY,
            setting_name VARCHAR(50) UNIQUE NOT NULL,
            setting_value TEXT NOT NULL,
            description TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    ");
    echo "✅ Created topup_settings table<br>\n";

    // Create qrcode_data table
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS qrcode_data (
            id INT AUTO_INCREMENT PRIMARY KEY,
            qr_data TEXT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            md5 VARCHAR(255) NOT NULL,
            username VARCHAR(100) NOT NULL,
            status ENUM('Pending', 'Successful', 'Failed', 'Expired') DEFAULT 'Pending',
            telegram_user_id INT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_md5 (md5),
            INDEX idx_username (username),
            INDEX idx_status (status)
        )
    ");
    echo "✅ Created qrcode_data table<br>\n";
    
    // Insert default settings if they don't exist
    $stmt = $pdo->query("SELECT COUNT(*) FROM topup_settings");
    if ($stmt->fetchColumn() == 0) {
        $pdo->exec("
            INSERT INTO topup_settings (setting_name, setting_value, description) VALUES 
            ('min_topup_amount', '10.00', 'Minimum amount for top-up'),
            ('max_topup_amount', '1000.00', 'Maximum amount for top-up'),
            ('topup_enabled', '1', 'Enable/disable top-up functionality'),
            ('qr_expiry_minutes', '30', 'QR code expiry time in minutes'),
            ('payment_gateway', 'manual', 'Payment gateway type (manual, auto)'),
            ('currency', 'USD', 'Currency for payments'),
            ('admin_topup_enabled', '1', 'Allow admin to add balance manually')
        ");
        echo "✅ Inserted default top-up settings<br>\n";
    } else {
        echo "ℹ️ Top-up settings already exist<br>\n";
    }
    
    // Create uploads/qr_codes directory if it doesn't exist
    $qr_dir = UPLOAD_PATH . 'qr_codes';
    if (!is_dir($qr_dir)) {
        mkdir($qr_dir, 0755, true);
        echo "✅ Created QR codes directory<br>\n";
    } else {
        echo "ℹ️ QR codes directory already exists<br>\n";
    }
    
    echo "<br><strong>✅ Balance system migration completed successfully!</strong><br>\n";
    echo "<p>The balance management system is now ready to use.</p>\n";
    echo "<p><a href='admin/dashboard.php'>Go to Admin Dashboard</a></p>\n";
    
} catch (PDOException $e) {
    echo "<strong>❌ Migration failed:</strong> " . $e->getMessage() . "<br>\n";
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Balance System Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 2rem; }
        h2 { color: #333; }
        p { margin: 1rem 0; }
        a { color: #3b82f6; text-decoration: none; }
        a:hover { text-decoration: underline; }
    </style>
</head>
<body>
</body>
</html>
