<?php
require_once 'config.php';

echo "<h2>🧪 Test QR Deletion System</h2>";

$chat_id = 1630035459; // Your Telegram ID

try {
    // Check if there are any pending QR transactions
    $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE chat_id = ? AND status = 'pending' ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$chat_id]);
    $pending_transactions = $stmt->fetchAll();
    
    echo "<h3>📋 Pending QR Transactions:</h3>";
    
    if ($pending_transactions) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f0f0f0;'><th>MD5</th><th>Amount</th><th>Message ID</th><th>Created</th><th>Age</th><th>Action</th></tr>";
        
        foreach ($pending_transactions as $tx) {
            $created_time = strtotime($tx['created_at']);
            $age_minutes = (time() - $created_time) / 60;
            $age_color = $age_minutes > 3 ? 'red' : 'green';
            
            echo "<tr>";
            echo "<td>" . substr($tx['md5'], 0, 8) . "...</td>";
            echo "<td>{$tx['amount']} USD</td>";
            echo "<td>{$tx['message_id']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td style='color: $age_color;'>" . round($age_minutes, 1) . " min</td>";
            
            if ($age_minutes > 3) {
                echo "<td><a href='?delete_md5={$tx['md5']}' style='color: red;'>🗑️ Delete Now</a></td>";
            } else {
                echo "<td>⏰ Waiting...</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Note:</strong> QR codes should auto-delete after 3 minutes</p>";
        
    } else {
        echo "<p>✅ No pending QR transactions found</p>";
    }
    
    // Manual deletion test
    if (isset($_GET['delete_md5'])) {
        $md5_to_delete = $_GET['delete_md5'];
        
        echo "<h3>🗑️ Manual QR Deletion Test</h3>";
        echo "<p>Testing deletion for MD5: " . substr($md5_to_delete, 0, 8) . "...</p>";
        
        // Get transaction details
        $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE md5 = ? AND chat_id = ?");
        $stmt->execute([$md5_to_delete, $chat_id]);
        $transaction = $stmt->fetch();
        
        if ($transaction) {
            echo "<p>📱 Found transaction with message_id: {$transaction['message_id']}</p>";
            
            // Try to delete the message
            $bot_token = TELEGRAM_BOT_TOKEN;
            $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
            
            $data = [
                'chat_id' => $chat_id,
                'message_id' => $transaction['message_id']
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            echo "<p><strong>Delete API Response:</strong> HTTP $http_code</p>";
            echo "<pre>$response</pre>";
            
            if ($http_code == 200) {
                // Update transaction status
                $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE md5 = ?");
                $stmt->execute([$md5_to_delete]);
                
                // Send expiration message
                $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
                $data = [
                    'chat_id' => $chat_id,
                    'text' => "⏰ QR code expired. Please generate a new one if needed."
                ];
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                
                $response = curl_exec($ch);
                curl_close($ch);
                
                echo "<p style='color: green;'>✅ QR message deleted and expiration message sent!</p>";
                echo "<p><a href='?' style='background: blue; color: white; padding: 10px; text-decoration: none;'>🔄 Refresh</a></p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to delete message</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Transaction not found</p>";
        }
    }
    
    // Check if exec() function is available
    echo "<h3>🔧 System Check:</h3>";
    
    if (function_exists('exec')) {
        echo "<p style='color: green;'>✅ exec() function is available</p>";
        
        // Test if background script can be executed
        $test_command = "php --version";
        $output = [];
        $return_var = 0;
        exec($test_command, $output, $return_var);
        
        if ($return_var === 0) {
            echo "<p style='color: green;'>✅ PHP CLI is working</p>";
            echo "<p>PHP Version: " . implode(' ', $output) . "</p>";
        } else {
            echo "<p style='color: red;'>❌ PHP CLI not working properly</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ exec() function is disabled</p>";
        echo "<p>Background QR deletion won't work without exec()</p>";
    }
    
    echo "<h3>🧪 Test QR Deletion:</h3>";
    echo "<ol>";
    echo "<li>Generate a new QR code with <strong>/topup</strong></li>";
    echo "<li>Don't pay for it</li>";
    echo "<li>Wait 3+ minutes</li>";
    echo "<li>Check if QR message gets deleted automatically</li>";
    echo "<li>Check if you receive expiration message</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
