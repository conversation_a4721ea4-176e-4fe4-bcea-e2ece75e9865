<?php
require_once 'config.php';

// Set JSON response header
header('Content-Type: application/json');

// Function to send JSON response
function sendResponse($success, $message, $data = null) {
    $response = [
        'success' => $success,
        'message' => $message
    ];
    
    if ($data !== null) {
        $response['data'] = $data;
    }
    
    echo json_encode($response);
    exit;
}

// Check request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendResponse(false, 'Only POST requests are allowed');
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

if (!$input) {
    sendResponse(false, 'Invalid JSON input');
}

// Validate required fields
$required_fields = ['telegram_id', 'amount', 'action'];
foreach ($required_fields as $field) {
    if (!isset($input[$field]) || empty($input[$field])) {
        sendResponse(false, "Missing required field: $field");
    }
}

$telegram_id = (int)$input['telegram_id'];
$amount = (float)$input['amount'];
$action = $input['action'];

// Validate action
if (!in_array($action, ['create_topup', 'check_status', 'cancel_topup'])) {
    sendResponse(false, 'Invalid action');
}

try {
    // Get or create telegram user
    $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
    $stmt->execute([$telegram_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        sendResponse(false, 'User not found. Please start the bot first.');
    }
    
    $telegram_user_id = $user['id'];
    
    // Get top-up settings
    $stmt = $pdo->query("SELECT setting_name, setting_value FROM topup_settings");
    $settings_data = $stmt->fetchAll();
    
    $settings = [];
    foreach ($settings_data as $setting) {
        $settings[$setting['setting_name']] = $setting['setting_value'];
    }
    
    // Check if top-up is enabled
    if (($settings['topup_enabled'] ?? '1') !== '1') {
        sendResponse(false, 'Top-up system is currently disabled');
    }
    
    switch ($action) {
        case 'create_topup':
            // Validate amount
            $min_amount = (float)($settings['min_topup_amount'] ?? 10);
            $max_amount = (float)($settings['max_topup_amount'] ?? 1000);
            
            if ($amount < $min_amount) {
                sendResponse(false, "Minimum top-up amount is $" . number_format($min_amount, 2));
            }
            
            if ($amount > $max_amount) {
                sendResponse(false, "Maximum top-up amount is $" . number_format($max_amount, 2));
            }
            
            // Check for existing pending requests
            $stmt = $pdo->prepare("
                SELECT id FROM topup_requests 
                WHERE telegram_user_id = ? AND status = 'pending' 
                AND expires_at > NOW()
            ");
            $stmt->execute([$telegram_user_id]);
            
            if ($stmt->fetch()) {
                sendResponse(false, 'You already have a pending top-up request. Please complete or wait for it to expire.');
            }
            
            // Generate unique transaction ID
            $transaction_id = 'TXN_' . time() . '_' . $telegram_id . '_' . rand(1000, 9999);
            
            // Calculate expiry time
            $expiry_minutes = (int)($settings['qr_expiry_minutes'] ?? 30);
            $expires_at = date('Y-m-d H:i:s', time() + ($expiry_minutes * 60));
            
            // Create top-up request
            $stmt = $pdo->prepare("
                INSERT INTO topup_requests 
                (telegram_user_id, amount, transaction_id, expires_at) 
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([$telegram_user_id, $amount, $transaction_id, $expires_at]);
            
            $topup_id = $pdo->lastInsertId();
            
            sendResponse(true, 'Top-up request created successfully', [
                'topup_id' => $topup_id,
                'transaction_id' => $transaction_id,
                'amount' => $amount,
                'expires_at' => $expires_at,
                'expiry_minutes' => $expiry_minutes
            ]);
            break;
            
        case 'check_status':
            $transaction_id = $input['transaction_id'] ?? '';
            
            if (empty($transaction_id)) {
                sendResponse(false, 'Transaction ID is required');
            }
            
            $stmt = $pdo->prepare("
                SELECT * FROM topup_requests 
                WHERE telegram_user_id = ? AND transaction_id = ?
            ");
            $stmt->execute([$telegram_user_id, $transaction_id]);
            $request = $stmt->fetch();
            
            if (!$request) {
                sendResponse(false, 'Top-up request not found');
            }
            
            // Check if expired
            if ($request['status'] === 'pending' && strtotime($request['expires_at']) < time()) {
                // Mark as expired
                $stmt = $pdo->prepare("UPDATE topup_requests SET status = 'expired' WHERE id = ?");
                $stmt->execute([$request['id']]);
                $request['status'] = 'expired';
            }
            
            sendResponse(true, 'Status retrieved successfully', [
                'status' => $request['status'],
                'amount' => $request['amount'],
                'created_at' => $request['created_at'],
                'expires_at' => $request['expires_at'],
                'completed_at' => $request['completed_at']
            ]);
            break;
            
        case 'cancel_topup':
            $transaction_id = $input['transaction_id'] ?? '';
            
            if (empty($transaction_id)) {
                sendResponse(false, 'Transaction ID is required');
            }
            
            $stmt = $pdo->prepare("
                UPDATE topup_requests 
                SET status = 'failed' 
                WHERE telegram_user_id = ? AND transaction_id = ? AND status = 'pending'
            ");
            $stmt->execute([$telegram_user_id, $transaction_id]);
            
            if ($stmt->rowCount() > 0) {
                sendResponse(true, 'Top-up request cancelled successfully');
            } else {
                sendResponse(false, 'No pending top-up request found to cancel');
            }
            break;
    }
    
} catch (PDOException $e) {
    error_log("Top-up request error: " . $e->getMessage());
    sendResponse(false, 'Database error occurred');
} catch (Exception $e) {
    error_log("Top-up request error: " . $e->getMessage());
    sendResponse(false, 'An error occurred while processing your request');
}
?>
