<?php
require_once 'config.php';

$chat_id = $_POST['chat_id'] ?? null;
$md5 = $_POST['md5'] ?? null;
$amount = $_POST['amount'] ?? null;
$message_id = $_POST['message_id'] ?? null;

if (!$chat_id || !$md5 || !$amount) {
    http_response_code(400);
    echo 'Missing parameters';
    exit;
}

try {
    // Mark as completed to prevent duplicates
    $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
    $stmt->execute([$chat_id, $md5]);
    
    if ($stmt->rowCount() == 0) {
        echo 'Already processed';
        exit;
    }
    
    // Update user balance
    $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE chat_id = ?");
    $stmt->execute([$amount, $chat_id]);
    
    // Send success message via Telegram API
    $bot_token = TELEGRAM_BOT_TOKEN;
    $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
    
    $data = [
        'chat_id' => $chat_id,
        'text' => "✅ Payment of {$amount} USD confirmed!\n💰 Balance updated automatically!",
        'parse_mode' => 'HTML'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    // Delete QR message
    if ($message_id) {
        $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
        $data = [
            'chat_id' => $chat_id,
            'message_id' => $message_id
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        curl_exec($ch);
        curl_close($ch);
    }
    
    // Remove from payment monitoring
    $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
    $stmt->execute([$chat_id, $md5]);
    
    error_log("Payment processed successfully: chat_id=$chat_id, amount=$amount");
    echo 'Payment processed successfully';
    
} catch (Exception $e) {
    error_log("Process payment error: " . $e->getMessage());
    http_response_code(500);
    echo 'Error processing payment';
}
?>
