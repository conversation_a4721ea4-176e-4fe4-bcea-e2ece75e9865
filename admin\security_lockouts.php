<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$message = '';

// Handle clearing lockouts
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['clear_ip'])) {
        $ip_to_clear = $_POST['clear_ip'];
        try {
            $stmt = $pdo->prepare("DELETE FROM admin_login_attempts WHERE ip_address = ?");
            $stmt->execute([$ip_to_clear]);
            $message = "✅ Cleared lockout for IP: " . htmlspecialchars($ip_to_clear);
        } catch (PDOException $e) {
            $message = "❌ Error clearing lockout: " . $e->getMessage();
        }
    } elseif (isset($_POST['clear_all'])) {
        try {
            $pdo->exec("DELETE FROM admin_login_attempts");
            $message = "✅ All lockouts cleared successfully.";
        } catch (PDOException $e) {
            $message = "❌ Error clearing all lockouts: " . $e->getMessage();
        }
    }
}

// Get current lockouts
try {
    $stmt = $pdo->query("
        SELECT ip_address, 
               COUNT(*) as total_attempts,
               MAX(attempt_time) as last_attempt,
               MIN(attempt_time) as first_attempt
        FROM admin_login_attempts 
        GROUP BY ip_address 
        ORDER BY last_attempt DESC
    ");
    $lockouts = $stmt->fetchAll();
} catch (PDOException $e) {
    $lockouts = [];
    $message = "❌ Error loading lockouts: " . $e->getMessage();
}

// Calculate lockout status for each IP
$lockout_periods = [
    1 => 1, 2 => 5, 3 => 10, 4 => 30, 5 => 60, 6 => 90, 7 => 1440
];

foreach ($lockouts as &$lockout) {
    $attempts = $lockout['total_attempts'];
    $lockout_minutes = $lockout_periods[min($attempts, 7)];
    
    // Calculate time remaining
    $last_attempt_time = strtotime($lockout['last_attempt']);
    $unlock_time = $last_attempt_time + ($lockout_minutes * 60);
    $now = time();
    
    if ($now < $unlock_time) {
        $lockout['status'] = 'locked';
        $remaining_seconds = $unlock_time - $now;
        $lockout['remaining_time'] = gmdate('H:i:s', $remaining_seconds);
        if ($remaining_seconds > 3600) {
            $lockout['remaining_time'] = floor($remaining_seconds / 3600) . 'h ' . gmdate('i:s', $remaining_seconds % 3600);
        }
    } else {
        $lockout['status'] = 'expired';
        $lockout['remaining_time'] = 'Expired';
    }
    
    $lockout['lockout_duration'] = $lockout_minutes . ' min';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Lockouts - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --sidebar-bg: #1e293b;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border: #e5e7eb;
            --hover: #f3f4f6;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            transform: translateX(4px);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .security-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.875rem;
            background: #d1fae5;
            color: #065f46;
        }
        
        /* Content */
        .content {
            padding: 2rem;
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        /* Stats Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            position: relative;
            overflow: hidden;
        }
        
        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--danger), #dc2626);
        }
        
        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .stat-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            background: linear-gradient(135deg, var(--danger), #dc2626);
        }
        
        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        /* Security Info */
        .security-info {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            margin-bottom: 2rem;
        }
        
        .info-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .info-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
        }
        
        .info-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .lockout-schedule {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .schedule-item {
            padding: 1rem;
            background: var(--hover);
            border-radius: 12px;
            text-align: center;
        }
        
        .schedule-attempt {
            font-weight: 700;
            color: var(--danger);
            font-size: 1.1rem;
        }
        
        .schedule-time {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        /* Lockouts Table */
        .lockouts-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            overflow: hidden;
        }
        
        .table-header {
            padding: 1.5rem 2rem;
            border-bottom: 1px solid var(--border);
            background: var(--hover);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .table-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .clear-all-btn {
            background: var(--danger);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .clear-all-btn:hover {
            background: #dc2626;
            transform: translateY(-1px);
        }
        
        .lockouts-table {
            width: 100%;
            border-collapse: collapse;
        }
        
        .lockouts-table th,
        .lockouts-table td {
            padding: 1rem 1.5rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }
        
        .lockouts-table th {
            background: var(--hover);
            font-weight: 600;
            color: var(--text-primary);
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .lockouts-table tr:hover {
            background: var(--hover);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-locked {
            background: #fee2e2;
            color: #991b1b;
        }
        
        .status-expired {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .ip-address {
            font-family: 'Courier New', monospace;
            background: var(--hover);
            padding: 0.25rem 0.5rem;
            border-radius: 6px;
            font-size: 0.875rem;
        }
        
        .btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.875rem;
        }
        
        .btn-success {
            background: var(--success);
            color: white;
        }
        
        .btn-success:hover {
            background: #059669;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .lockout-schedule {
                grid-template-columns: 1fr;
            }
            
            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .table-header {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link active">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Security Center</h1>
            <div class="security-status">
                <i class="fas fa-shield-check"></i>
                Progressive Lockout Active
            </div>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?php echo strpos($message, '✅') !== false ? 'alert-success' : 'alert-error'; ?>">
                    <i class="fas fa-<?php echo strpos($message, '✅') !== false ? 'check-circle' : 'exclamation-triangle'; ?>"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total IPs Tracked</div>
                        <div class="stat-icon">
                            <i class="fas fa-globe"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo count($lockouts); ?></div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Currently Locked</div>
                        <div class="stat-icon">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo count(array_filter($lockouts, fn($l) => $l['status'] === 'locked')); ?></div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-header">
                        <div class="stat-title">Total Attempts</div>
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                    <div class="stat-value"><?php echo array_sum(array_column($lockouts, 'total_attempts')); ?></div>
                </div>
            </div>
            
            <!-- Security Information -->
            <div class="security-info">
                <div class="info-header">
                    <div class="info-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="info-title">Progressive Lockout System</div>
                </div>
                
                <div class="lockout-schedule">
                    <div class="schedule-item">
                        <div class="schedule-attempt">1st Attempt</div>
                        <div class="schedule-time">1 minute lockout</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-attempt">2nd Attempt</div>
                        <div class="schedule-time">5 minutes lockout</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-attempt">3rd Attempt</div>
                        <div class="schedule-time">10 minutes lockout</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-attempt">4th Attempt</div>
                        <div class="schedule-time">30 minutes lockout</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-attempt">5th Attempt</div>
                        <div class="schedule-time">1 hour lockout</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-attempt">6th Attempt</div>
                        <div class="schedule-time">1.5 hours lockout</div>
                    </div>
                    <div class="schedule-item">
                        <div class="schedule-attempt">7th+ Attempts</div>
                        <div class="schedule-time">1 day lockout</div>
                    </div>
                </div>
                
                <p style="color: var(--text-secondary); text-align: center;">
                    ✅ Successful login clears all failed attempts for that IP address.
                </p>
            </div>
            
            <!-- Lockouts Table -->
            <div class="lockouts-container">
                <div class="table-header">
                    <h2 class="table-title">Active Security Lockouts</h2>
                    <?php if (!empty($lockouts)): ?>
                        <form method="POST" style="display: inline;">
                            <button type="submit" name="clear_all" class="clear-all-btn" 
                                    onclick="return confirm('Clear all lockouts? This will reset all failed attempt counters.')">
                                <i class="fas fa-trash"></i> Clear All
                            </button>
                        </form>
                    <?php endif; ?>
                </div>
                
                <?php if (empty($lockouts)): ?>
                    <div class="empty-state">
                        <i class="fas fa-shield-check"></i>
                        <h3>No Security Threats Detected</h3>
                        <p>All systems secure. No failed login attempts recorded.</p>
                    </div>
                <?php else: ?>
                    <table class="lockouts-table">
                        <thead>
                            <tr>
                                <th>IP Address</th>
                                <th>Failed Attempts</th>
                                <th>Lockout Duration</th>
                                <th>Status</th>
                                <th>Time Remaining</th>
                                <th>First Attempt</th>
                                <th>Last Attempt</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($lockouts as $lockout): ?>
                                <tr>
                                    <td>
                                        <span class="ip-address"><?php echo htmlspecialchars($lockout['ip_address']); ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo $lockout['total_attempts']; ?></strong>
                                    </td>
                                    <td><?php echo $lockout['lockout_duration']; ?></td>
                                    <td>
                                        <span class="status-badge status-<?php echo $lockout['status']; ?>">
                                            <?php echo $lockout['status'] === 'locked' ? '🔒 Locked' : '⏰ Expired'; ?>
                                        </span>
                                    </td>
                                    <td><?php echo $lockout['remaining_time']; ?></td>
                                    <td><?php echo date('M j, H:i', strtotime($lockout['first_attempt'])); ?></td>
                                    <td><?php echo date('M j, H:i', strtotime($lockout['last_attempt'])); ?></td>
                                    <td>
                                        <form method="POST" style="display: inline;">
                                            <input type="hidden" name="clear_ip" value="<?php echo htmlspecialchars($lockout['ip_address']); ?>">
                                            <button type="submit" class="btn btn-success">
                                                <i class="fas fa-unlock"></i> Clear
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 30 seconds to update remaining times
        setTimeout(() => location.reload(), 30000);
    </script>
</body>
</html>
