<?php
require_once 'config.php';

// Redirect if already logged in
if (is_admin_logged_in()) {
    redirect('admin/dashboard.php');
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verify_csrf_token($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token. Please try again.';
    } else {
        $username = sanitize_input($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            $error_message = 'Please enter both username and password.';
        } else {
            try {
                // Progressive lockout system with escalating timeouts
                $ip = $_SERVER['REMOTE_ADDR'];

                // TEMPORARY BYPASS - Remove this line when you're done testing
                $bypass_lockout = false; // Set to false to re-enable lockouts

                // Create login attempts table if it doesn't exist
                $pdo->exec("
                    CREATE TABLE IF NOT EXISTS admin_login_attempts (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        ip_address VARCHAR(45) NOT NULL,
                        username VARCHAR(50),
                        attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_ip_time (ip_address, attempt_time)
                    )
                ");

                // Get total failed attempts for this IP
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) as total_attempts,
                           MAX(attempt_time) as last_attempt
                    FROM admin_login_attempts
                    WHERE ip_address = ?
                ");
                $stmt->execute([$ip]);
                $attempt_data = $stmt->fetch();
                $total_attempts = $attempt_data['total_attempts'] ?? 0;
                $last_attempt = $attempt_data['last_attempt'];

                // Define progressive lockout periods (in minutes)
                $lockout_periods = [
                    1 => 1,      // 1st failed attempt: 1 minute
                    2 => 5,      // 2nd failed attempt: 5 minutes
                    3 => 10,     // 3rd failed attempt: 10 minutes
                    4 => 30,     // 4th failed attempt: 30 minutes
                    5 => 60,     // 5th failed attempt: 60 minutes
                    6 => 90,     // 6th failed attempt: 90 minutes
                    7 => 1440    // 7th+ failed attempt: 1 day (1440 minutes)
                ];

                // Check if still in lockout period
                $is_locked = false;
                $remaining_time = '';
                if ($total_attempts > 0 && $last_attempt) {
                    $lockout_minutes = $lockout_periods[min($total_attempts, 7)];

                    $stmt = $pdo->prepare("
                        SELECT TIMESTAMPDIFF(MINUTE, ?, NOW()) as minutes_passed
                    ");
                    $stmt->execute([$last_attempt]);
                    $minutes_passed = $stmt->fetchColumn();

                    if ($minutes_passed < $lockout_minutes) {
                        $is_locked = true;
                        $remaining_minutes = $lockout_minutes - $minutes_passed;

                        // Format remaining time nicely
                        if ($remaining_minutes >= 1440) {
                            $days = floor($remaining_minutes / 1440);
                            $hours = floor(($remaining_minutes % 1440) / 60);
                            $remaining_time = $days . ' day' . ($days > 1 ? 's' : '');
                            if ($hours > 0) $remaining_time .= ' and ' . $hours . ' hour' . ($hours > 1 ? 's' : '');
                        } elseif ($remaining_minutes >= 60) {
                            $hours = floor($remaining_minutes / 60);
                            $mins = $remaining_minutes % 60;
                            $remaining_time = $hours . ' hour' . ($hours > 1 ? 's' : '');
                            if ($mins > 0) $remaining_time .= ' and ' . $mins . ' minute' . ($mins > 1 ? 's' : '');
                        } else {
                            $remaining_time = $remaining_minutes . ' minute' . ($remaining_minutes > 1 ? 's' : '');
                        }
                    }
                }

                if ($is_locked && !$bypass_lockout) {
                    $attempt_word = $total_attempts == 1 ? 'attempt' : 'attempts';
                    $error_message = "🔒 Account locked after {$total_attempts} failed {$attempt_word}. Try again in {$remaining_time}.";
                } else {
                    // Verify credentials
                    $stmt = $pdo->prepare("SELECT id, username, password FROM admin_users WHERE username = ?");
                    $stmt->execute([$username]);
                    $admin = $stmt->fetch();
                    
                    if ($admin && password_verify($password, $admin['password'])) {
                        // Successful login
                        $_SESSION['admin_id'] = $admin['id'];
                        $_SESSION['admin_username'] = $admin['username'];
                        $_SESSION['login_time'] = time();
                        
                        // Update last login
                        $stmt = $pdo->prepare("UPDATE admin_users SET last_login = NOW() WHERE id = ?");
                        $stmt->execute([$admin['id']]);
                        
                        // Clear failed attempts
                        $stmt = $pdo->prepare("DELETE FROM admin_login_attempts WHERE ip_address = ?");
                        $stmt->execute([$ip]);
                        
                        redirect('admin/dashboard.php');
                    } else {
                        // Failed login
                        $stmt = $pdo->prepare("INSERT INTO admin_login_attempts (ip_address, username) VALUES (?, ?)");
                        $stmt->execute([$ip, $username]);
                        
                        $error_message = 'Invalid username or password.';
                    }
                }
            } catch (PDOException $e) {
                $error_message = 'Login system error. Please try again.';
                error_log("Login error: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Management System - Admin Portal</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --dark-gradient: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 50%, #2d2d2d 100%);
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.12);
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --accent-blue: #00d4ff;
            --accent-purple: #8b5cf6;
            --success-green: #10b981;
            --error-red: #ef4444;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--dark-gradient);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Advanced Background Animation */
        .bg-canvas {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        .particle {
            position: absolute;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.8) 0%, transparent 70%);
            border-radius: 50%;
            pointer-events: none;
            animation: particleFloat 8s infinite ease-in-out;
        }

        @keyframes particleFloat {
            0%, 100% {
                transform: translateY(0px) translateX(0px) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-20px) translateX(10px) scale(1.1);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-40px) translateX(-10px) scale(0.9);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-20px) translateX(15px) scale(1.05);
                opacity: 0.4;
            }
        }

        /* Mesh Gradient Background */
        .mesh-gradient {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            animation: meshMove 20s ease-in-out infinite;
        }

        @keyframes meshMove {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.1) rotate(5deg); }
        }

        /* Premium Login Container */
        .login-wrapper {
            position: relative;
            z-index: 10;
        }

        .login-container {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            padding: 3.5rem;
            border-radius: 28px;
            box-shadow:
                0 32px 64px rgba(0, 0, 0, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            width: 100%;
            max-width: 480px;
            position: relative;
            animation: containerSlideIn 1.2s cubic-bezier(0.16, 1, 0.3, 1);
            overflow: hidden;
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes containerSlideIn {
            0% {
                opacity: 0;
                transform: translateY(60px) scale(0.9);
                filter: blur(10px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        @keyframes shimmer {
            0%, 100% { opacity: 0; }
            50% { opacity: 1; }
        }

        /* Logo and Header */
        .login-header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .logo-container {
            position: relative;
            display: inline-block;
            margin-bottom: 2rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: var(--primary-gradient);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            position: relative;
            box-shadow:
                0 20px 40px rgba(102, 126, 234, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: logoFloat 4s ease-in-out infinite;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: conic-gradient(from 0deg, var(--accent-blue), var(--accent-purple), var(--accent-blue));
            border-radius: 26px;
            z-index: -1;
            animation: rotate 4s linear infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px) scale(1); }
            50% { transform: translateY(-8px) scale(1.05); }
        }

        @keyframes rotate {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .logo i {
            color: white;
            font-size: 2rem;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }
        
        .login-header h1 {
            color: var(--text-primary);
            margin-bottom: 0.75rem;
            font-size: 2.25rem;
            font-weight: 700;
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--accent-blue) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGlow 3s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            0% { filter: brightness(1); }
            100% { filter: brightness(1.2); }
        }

        .login-header p {
            color: var(--text-secondary);
            font-size: 1rem;
            font-weight: 400;
            opacity: 0.8;
        }

        /* Premium Form Styling */
        .form-container {
            position: relative;
        }
        
        .form-group {
            margin-bottom: 2rem;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 1rem;
            color: var(--text-primary);
            font-weight: 600;
            font-size: 0.95rem;
            letter-spacing: 0.02em;
            text-transform: uppercase;
            opacity: 0.9;
        }

        .input-wrapper {
            position: relative;
            group: input-group;
        }

        .input-wrapper::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
            border-radius: 16px;
            padding: 2px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .input-wrapper.focused::before {
            opacity: 1;
        }

        .input-wrapper i {
            position: absolute;
            left: 1.25rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            font-size: 1.1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            z-index: 2;
        }

        .form-group input {
            width: 100%;
            padding: 1.25rem 1.25rem 1.25rem 3.25rem;
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            font-size: 1.05rem;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-primary);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-weight: 400;
            backdrop-filter: blur(10px);
            position: relative;
            z-index: 1;
        }

        .form-group input::placeholder {
            color: var(--text-secondary);
            opacity: 0.6;
        }

        .form-group input:focus {
            outline: none;
            border-color: transparent;
            background: rgba(255, 255, 255, 0.08);
            box-shadow:
                0 0 0 1px rgba(255, 255, 255, 0.1),
                0 8px 32px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .form-group input:focus + i {
            color: var(--accent-blue);
            transform: translateY(-50%) scale(1.1);
        }
        
        /* Premium Button Design */
        .login-btn {
            width: 100%;
            padding: 1.25rem 2rem;
            background: var(--primary-gradient);
            color: white;
            border: none;
            border-radius: 16px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            text-transform: uppercase;
            letter-spacing: 0.05em;
            box-shadow:
                0 8px 32px rgba(102, 126, 234, 0.4),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .login-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            transition: left 0.6s ease;
        }

        .login-btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s ease, height 0.6s ease;
        }

        .login-btn:hover::before {
            left: 100%;
        }

        .login-btn:hover::after {
            width: 300px;
            height: 300px;
        }

        .login-btn:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow:
                0 20px 40px rgba(102, 126, 234, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.1);
        }

        .login-btn:active {
            transform: translateY(-2px) scale(0.98);
        }

        .login-btn.loading {
            pointer-events: none;
            opacity: 0.8;
        }

        .login-btn.loading span {
            opacity: 0;
        }

        .login-btn.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 24px;
            height: 24px;
            margin: -12px 0 0 -12px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top-color: white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Premium Alert Styling */
        .alert {
            padding: 1.25rem 1.5rem;
            border-radius: 16px;
            margin-bottom: 2rem;
            display: flex;
            align-items: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            animation: alertSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: currentColor;
            opacity: 0.8;
        }

        @keyframes alertSlideIn {
            0% {
                opacity: 0;
                transform: translateY(-20px) scale(0.95);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            color: #fca5a5;
            border-color: rgba(239, 68, 68, 0.2);
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: #6ee7b7;
            border-color: rgba(16, 185, 129, 0.2);
        }

        .alert i {
            margin-right: 1rem;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        
        /* Footer Styling */
        .footer {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 400;
            opacity: 0.7;
        }

        .footer a {
            color: var(--accent-blue);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .footer a:hover {
            color: white;
            text-shadow: 0 0 10px var(--accent-blue);
        }

        /* Enhanced Mobile Experience */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
                background-attachment: fixed;
            }

            .login-container {
                margin: 0;
                padding: 2.5rem 2rem;
                max-width: 100%;
                min-height: calc(100vh - 2rem);
                display: flex;
                flex-direction: column;
                justify-content: center;
                border-radius: 20px;
            }

            .login-header {
                margin-bottom: 2rem;
            }

            .login-header h1 {
                font-size: 2rem;
                line-height: 1.2;
            }

            .logo {
                width: 70px;
                height: 70px;
            }

            .logo i {
                font-size: 1.75rem;
            }

            .form-group {
                margin-bottom: 1.75rem;
            }

            .form-group input {
                padding: 1.125rem 1rem 1.125rem 3.25rem;
                font-size: 1.05rem;
                border-radius: 14px;
            }

            .input-wrapper i {
                font-size: 1.15rem;
                left: 1.125rem;
            }

            .login-btn {
                padding: 1.125rem 1.5rem;
                font-size: 1.05rem;
                border-radius: 14px;
            }

            .alert {
                padding: 1rem 1.25rem;
                font-size: 0.95rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 0.5rem;
            }

            .login-container {
                padding: 2rem 1.5rem;
                border-radius: 16px;
            }

            .login-header h1 {
                font-size: 1.75rem;
            }

            .login-header p {
                font-size: 0.9rem;
            }

            .logo {
                width: 60px;
                height: 60px;
                margin-bottom: 1.25rem;
            }

            .logo i {
                font-size: 1.5rem;
            }

            .form-group input {
                padding: 1rem 0.875rem 1rem 3rem;
                font-size: 1rem;
            }

            .input-wrapper i {
                left: 1rem;
                font-size: 1.1rem;
            }

            .login-btn {
                padding: 1rem 1.25rem;
                font-size: 1rem;
            }
        }

        /* Touch Device Optimizations */
        @media (hover: none) and (pointer: coarse) {
            .login-btn:hover {
                transform: none;
                box-shadow: 0 8px 32px rgba(102, 126, 234, 0.4);
            }

            .login-btn:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }

            .form-group input:focus {
                transform: none;
            }
        }

        /* Advanced Animations */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Premium Enhancements */
        .login-container::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(102, 126, 234, 0.03), transparent, rgba(139, 92, 246, 0.03), transparent);
            animation: rotate 20s linear infinite;
            z-index: -1;
            border-radius: 50%;
        }

        /* Enhanced Input Validation Feedback */
        .form-group input:valid {
            border-color: rgba(16, 185, 129, 0.3);
            box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.1);
        }

        .form-group input:invalid:not(:placeholder-shown) {
            border-color: rgba(239, 68, 68, 0.3);
            box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.1);
        }

        /* Smooth Page Transitions */
        .page-transition {
            opacity: 0;
            animation: pageLoad 1.5s cubic-bezier(0.16, 1, 0.3, 1) forwards;
        }

        @keyframes pageLoad {
            0% {
                opacity: 0;
                transform: translateY(20px) scale(0.98);
                filter: blur(5px);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
                filter: blur(0);
            }
        }

        /* Enhanced Security Indicator */
        .security-badge {
            position: absolute;
            top: 1rem;
            right: 1rem;
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            border: 1px solid rgba(16, 185, 129, 0.2);
            backdrop-filter: blur(10px);
        }

        .security-badge i {
            margin-right: 0.5rem;
        }

        /* Enhanced Footer */
        .footer {
            position: fixed;
            bottom: 1rem;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.8rem;
            font-weight: 400;
            opacity: 0.6;
            backdrop-filter: blur(10px);
            background: rgba(0, 0, 0, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Dark Mode Enhancement */
        @media (prefers-color-scheme: dark) {
            :root {
                --glass-bg: rgba(0, 0, 0, 0.2);
                --glass-border: rgba(255, 255, 255, 0.1);
            }
        }

        /* High DPI Display Optimization */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .logo {
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
            }
        }
    </style>
</head>
<body class="page-transition">
    <!-- Advanced Background -->
    <div class="bg-canvas">
        <div class="mesh-gradient"></div>
    </div>

    <div class="login-wrapper">
        <div class="login-container">
            <!-- Security Badge -->
            <div class="security-badge">
                <i class="fas fa-shield-check"></i>
                SSL Secured
            </div>

            <div class="login-header">
                <div class="logo-container">
                    <div class="logo">
                        <i class="fas fa-crown"></i>
                    </div>
                </div>
                <h1>Admin Portal</h1>
                <p>Secure Account Management System</p>
            </div>

            <div class="form-container">
        
                <?php if ($error_message): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" action="" id="loginForm">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">

                    <div class="form-group">
                        <label for="username">Username</label>
                        <div class="input-wrapper">
                            <input type="text" id="username" name="username" required
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>"
                                   placeholder="Enter your admin username"
                                   autocomplete="username"
                                   autocapitalize="none"
                                   spellcheck="false">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-wrapper">
                            <input type="password" id="password" name="password" required
                                   placeholder="Enter your secure password"
                                   autocomplete="current-password"
                                   minlength="6">
                            <i class="fas fa-lock"></i>
                        </div>
                    </div>

                    <button type="submit" class="login-btn" id="loginBtn">
                        <span>Access Portal</span>
                    </button>
                </form>
            </div>
        </div>
    </div>
        

    <div class="footer">
        <p>&copy; 2024 Account Management System | <a href="#">Secure Admin Portal</a></p>
    </div>

    <script>
        // Enhanced particle system with mobile optimization
        function createParticles() {
            const canvas = document.querySelector('.bg-canvas');
            const isMobile = window.innerWidth <= 768;
            const particleCount = isMobile ? 8 : 15; // Fewer particles on mobile for performance

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                const size = Math.random() * (isMobile ? 3 : 4) + 2;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 8) + 's';
                canvas.appendChild(particle);
            }
        }

        // Mobile-specific optimizations
        function initMobileOptimizations() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                // Prevent zoom on input focus
                const viewport = document.querySelector('meta[name=viewport]');
                if (viewport) {
                    viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
                }

                // Add mobile-specific class
                document.body.classList.add('mobile-device');

                // Optimize animations for mobile
                document.documentElement.style.setProperty('--animation-speed', '0.5');
            }
        }

        // Enhanced form interactions with mobile support
        document.addEventListener('DOMContentLoaded', function() {
            initMobileOptimizations();
            createParticles();

            const form = document.getElementById('loginForm');
            const btn = document.getElementById('loginBtn');
            const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
            const isMobile = window.innerWidth <= 768;

            // Form submission with enhanced loading state
            form.addEventListener('submit', function(e) {
                btn.classList.add('loading');

                // Add haptic feedback on mobile
                if ('vibrate' in navigator) {
                    navigator.vibrate(50);
                }
            });

            // Enhanced input focus effects with mobile optimization
            inputs.forEach(input => {
                const wrapper = input.parentElement;
                const icon = wrapper.querySelector('i');

                input.addEventListener('focus', function() {
                    wrapper.classList.add('focused');
                    icon.style.color = 'var(--accent-blue)';
                    icon.style.transform = 'translateY(-50%) scale(1.1)';

                    // Scroll input into view on mobile
                    if (isMobile) {
                        setTimeout(() => {
                            this.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }, 300);
                    }
                });

                input.addEventListener('blur', function() {
                    if (!this.value) {
                        wrapper.classList.remove('focused');
                        icon.style.color = 'var(--text-secondary)';
                        icon.style.transform = 'translateY(-50%) scale(1)';
                    }
                });

                // Real-time validation feedback
                input.addEventListener('input', function() {
                    if (this.value.length > 0) {
                        wrapper.classList.add('focused');
                        icon.style.color = 'var(--accent-blue)';
                    }
                });

                // Maintain focus state if input has value
                if (input.value) {
                    wrapper.classList.add('focused');
                    icon.style.color = 'var(--accent-blue)';
                }
            });

            // Parallax effect (disabled on mobile for performance)
            if (!isMobile) {
                document.addEventListener('mousemove', function(e) {
                    const mouseX = e.clientX / window.innerWidth;
                    const mouseY = e.clientY / window.innerHeight;

                    const container = document.querySelector('.login-container');
                    const translateX = (mouseX - 0.5) * 8;
                    const translateY = (mouseY - 0.5) * 8;

                    container.style.transform = `translate(${translateX}px, ${translateY}px)`;
                });
            }

            // Enhanced keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' && document.activeElement.tagName !== 'BUTTON') {
                    const nextInput = document.querySelector('input:not([value]):not(:focus)');
                    if (nextInput) {
                        nextInput.focus();
                    } else {
                        form.submit();
                    }
                }
            });

            // Auto-hide address bar on mobile
            if (isMobile) {
                window.addEventListener('load', function() {
                    setTimeout(() => {
                        window.scrollTo(0, 1);
                    }, 100);
                });
            }
        });
    </script>
</body>
</html>
