# Account Management System

A complete PHP-based system for managing and selling gaming accounts through a web admin panel and Telegram bot integration.

## Features

### Admin Panel
- 🔐 Secure login system with session management
- 📊 Dashboard with sales statistics and analytics
- 📦 Account management (add, edit, delete accounts)
- 🖼️ Image upload for account previews
- 💰 Purchase management and order processing
- 🤖 Telegram bot configuration and monitoring
- 👥 User management and interaction tracking

### Telegram Bot
- 🛍️ Browse available accounts with images
- 💳 Purchase initiation and payment processing
- 📱 Interactive inline keyboard navigation
- 📞 Support contact integration
- 📦 Purchase history for users
- 🔄 Real-time account availability

### Security Features
- 🛡️ CSRF protection
- 🔒 Password hashing with PHP's password_hash()
- 🚫 Rate limiting for login attempts
- 📁 Secure file upload handling
- 🔐 Session timeout management

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web server (Apache/Nginx)
- cURL extension for Telegram API
- GD extension for image handling
- PDO MySQL extension

## Installation

### 1. Download and Setup Files

1. Clone or download this repository to your web server
2. Place files in your web root directory (e.g., `/var/www/html/` or `c:\xampp\htdocs\`)

### 2. Database Configuration

1. Create a MySQL database for the application
2. Update database credentials in `config.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   define('DB_NAME', 'your_database_name');
   ```

### 3. Telegram Bot Setup

1. Create a new bot with [@BotFather](https://t.me/botfather) on Telegram
2. Get your bot token
3. Update the bot token in `config.php`:
   ```php
   define('TELEGRAM_BOT_TOKEN', 'your_bot_token_here');
   ```

### 4. Site Configuration

1. Update your site URL in `config.php`:
   ```php
   define('SITE_URL', 'https://yourdomain.com');
   ```

### 5. Run Setup

1. Navigate to `https://yourdomain.com/setup.php` in your browser
2. Follow the setup instructions
3. The setup will:
   - Create database tables
   - Create upload directories
   - Set up security files
   - Create default admin user

### 6. Initial Configuration

1. Login to admin panel with:
   - Username: `admin`
   - Password: `admin123`
2. **IMMEDIATELY** change the default password
3. Configure Telegram webhook in Bot Settings
4. Add your first account

## File Structure

```
/
├── index.php              # Admin login page
├── config.php             # Configuration file
├── setup.php              # Setup script
├── telegram_bot.php       # Telegram webhook handler
├── database.sql           # Database schema
├── admin/                 # Admin panel files
│   ├── dashboard.php      # Main dashboard
│   ├── accounts.php       # Account management
│   ├── add_account.php    # Add new account
│   ├── purchases.php      # Purchase management
│   ├── bot_webhook.php    # Bot configuration
│   └── logout.php         # Logout handler
└── uploads/               # Upload directories
    ├── accounts/          # Account images
    ├── qr_codes/          # QR codes (future use)
    └── account_files/     # Generated account files
```

## Usage

### Admin Panel

1. **Login**: Access the admin panel at your domain root
2. **Dashboard**: View statistics and recent activity
3. **Add Accounts**: Upload account details and images
4. **Manage Purchases**: Process payments and deliver accounts
5. **Bot Settings**: Configure Telegram webhook

### Telegram Bot

1. Users start the bot with `/start`
2. Browse accounts with inline keyboards
3. View account details and images
4. Initiate purchases
5. Receive account details after payment confirmation

### Processing Orders

1. Customer initiates purchase through Telegram bot
2. Purchase appears in admin panel with "Pending" status
3. Admin confirms payment and clicks "Complete"
4. System automatically:
   - Marks account as sold
   - Generates account.txt file
   - Sends account details to customer via Telegram
   - Updates purchase status

## Security Considerations

### Production Deployment

1. **HTTPS**: Always use HTTPS in production
2. **Database**: Use strong database passwords
3. **File Permissions**: Set appropriate file permissions (755 for directories, 644 for files)
4. **Error Reporting**: Disable error reporting in production:
   ```php
   error_reporting(0);
   ini_set('display_errors', 0);
   ```

### Regular Maintenance

1. Regularly backup your database
2. Monitor upload directory sizes
3. Review access logs for suspicious activity
4. Keep PHP and server software updated

## Customization

### Adding Payment Methods

The system is designed to be extended with payment gateways. You can:

1. Add payment processing in `admin/purchases.php`
2. Integrate QR code generation for crypto payments
3. Add webhook handlers for payment confirmations

### Styling

- Modify CSS in individual PHP files
- Create a separate CSS file for consistent styling
- Customize the Telegram bot messages in `telegram_bot.php`

### Features

- Add account categories
- Implement automatic account delivery
- Add email notifications
- Create customer reviews system

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config.php`
   - Verify database server is running

2. **File Upload Issues**
   - Check directory permissions
   - Verify PHP upload settings (`upload_max_filesize`, `post_max_size`)

3. **Telegram Bot Not Responding**
   - Verify bot token is correct
   - Check webhook URL is accessible
   - Review bot interaction logs in admin panel

4. **Session Issues**
   - Check PHP session configuration
   - Verify session directory permissions

### Support

For technical support or questions:
- Check the admin panel logs
- Review PHP error logs
- Verify all configuration settings

## License

This project is provided as-is for educational and commercial use. Please ensure compliance with local laws and Telegram's Terms of Service when operating a commercial bot.

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve the system.
