<?php
require_once 'config.php';

echo "<h2>🗑️ Test Telegram Message Deletion</h2>";

$chat_id = 1630035459; // Your Telegram ID

try {
    // Get the most recent QR transaction
    $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE chat_id = ? AND status = 'pending' ORDER BY created_at DESC LIMIT 1");
    $stmt->execute([$chat_id]);
    $transaction = $stmt->fetch();
    
    if ($transaction) {
        echo "<h3>📋 Found QR Transaction:</h3>";
        echo "<p><strong>MD5:</strong> " . substr($transaction['md5'], 0, 8) . "...</p>";
        echo "<p><strong>Message ID:</strong> {$transaction['message_id']}</p>";
        echo "<p><strong>Amount:</strong> {$transaction['amount']} USD</p>";
        echo "<p><strong>Created:</strong> {$transaction['created_at']}</p>";
        
        $age_minutes = (time() - strtotime($transaction['created_at'])) / 60;
        echo "<p><strong>Age:</strong> " . round($age_minutes, 1) . " minutes</p>";
        
        if (isset($_GET['delete_now'])) {
            echo "<h3>🗑️ Attempting to Delete Message...</h3>";
            
            $bot_token = TELEGRAM_BOT_TOKEN;
            $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
            
            $data = [
                'chat_id' => $chat_id,
                'message_id' => $transaction['message_id']
            ];
            
            echo "<p><strong>API URL:</strong> $api_url</p>";
            echo "<p><strong>Data:</strong> " . json_encode($data) . "</p>";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_VERBOSE, true);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            echo "<p><strong>HTTP Code:</strong> $http_code</p>";
            echo "<p><strong>Response:</strong> <pre>$response</pre></p>";
            
            if ($curl_error) {
                echo "<p><strong>cURL Error:</strong> $curl_error</p>";
            }
            
            // Parse response
            $response_data = json_decode($response, true);
            
            if ($http_code == 200 && $response_data && $response_data['ok']) {
                echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS! Message deleted from Telegram!</p>";
                
                // Update database
                $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE id = ?");
                $stmt->execute([$transaction['id']]);
                
                echo "<p>Database updated to 'expired' status.</p>";
                echo "<p><a href='?' style='background: blue; color: white; padding: 10px; text-decoration: none;'>🔄 Refresh</a></p>";
                
            } else {
                echo "<p style='color: red; font-weight: bold;'>❌ FAILED to delete message</p>";
                
                if ($response_data && isset($response_data['description'])) {
                    echo "<p><strong>Error:</strong> " . $response_data['description'] . "</p>";
                    
                    // Common error explanations
                    if (strpos($response_data['description'], 'message to delete not found') !== false) {
                        echo "<p style='background: #ffe6e6; padding: 10px;'>";
                        echo "<strong>Possible reasons:</strong><br>";
                        echo "• Message was already deleted<br>";
                        echo "• Message ID is incorrect<br>";
                        echo "• Message is too old (>48 hours)<br>";
                        echo "</p>";
                    }
                    
                    if (strpos($response_data['description'], 'not enough rights') !== false) {
                        echo "<p style='background: #ffe6e6; padding: 10px;'>";
                        echo "<strong>Bot Permission Issue:</strong><br>";
                        echo "• Bot doesn't have permission to delete messages<br>";
                        echo "• Check bot admin settings in the chat<br>";
                        echo "</p>";
                    }
                }
            }
            
        } else {
            echo "<p><a href='?delete_now=1' style='background: red; color: white; padding: 10px; text-decoration: none;'>🗑️ Test Delete This Message</a></p>";
        }
        
    } else {
        echo "<p>No pending QR transactions found. Generate a QR code first with <code>/topup</code></p>";
    }
    
    echo "<h3>🔧 Bot Information:</h3>";
    
    // Test bot info
    $bot_token = TELEGRAM_BOT_TOKEN;
    $api_url = "https://api.telegram.org/bot{$bot_token}/getMe";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    $bot_info = json_decode($response, true);
    
    if ($bot_info && $bot_info['ok']) {
        echo "<p><strong>Bot Username:</strong> @" . $bot_info['result']['username'] . "</p>";
        echo "<p><strong>Bot Name:</strong> " . $bot_info['result']['first_name'] . "</p>";
        echo "<p><strong>Can Delete Messages:</strong> " . ($bot_info['result']['can_delete_messages'] ?? 'Unknown') . "</p>";
    }
    
    echo "<h3>📝 Instructions:</h3>";
    echo "<ol>";
    echo "<li>Generate a QR code with <code>/topup</code> command</li>";
    echo "<li>Come back to this page and click 'Test Delete This Message'</li>";
    echo "<li>Check if the QR message disappears from Telegram</li>";
    echo "<li>If it fails, check the error message for details</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
