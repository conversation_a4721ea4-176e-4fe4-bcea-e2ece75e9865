<?php
/**
 * QR Code Cleanup Cron Job
 *
 * This script should be run every 10 seconds to:
 * 1. Delete expired QR code messages from Telegram chats (after 3 minutes)
 * 2. Send expiration messages to users
 * 3. Clean up timer files and database records
 *
 * Add to crontab (every 10 seconds):
 * * * * * * /usr/bin/php /path/to/your/qr_cleanup_cron.php
 * * * * * * sleep 10; /usr/bin/php /path/to/your/qr_cleanup_cron.php
 * * * * * * sleep 20; /usr/bin/php /path/to/your/qr_cleanup_cron.php
 * * * * * * sleep 30; /usr/bin/php /path/to/your/qr_cleanup_cron.php
 * * * * * * sleep 40; /usr/bin/php /path/to/your/qr_cleanup_cron.php
 * * * * * * sleep 50; /usr/bin/php /path/to/your/qr_cleanup_cron.php
 */

require_once 'config.php';
require_once 'telegram_bot.php';

// Set time limit for cron job
set_time_limit(60);

echo "[" . date('Y-m-d H:i:s') . "] Starting QR cleanup process...\n";

try {
    // Process QR deletion timers (delete expired QR messages from Telegram)
    processQRDeletionTimers();

    // Also clean up old monitoring records (older than 1 hour)
    cleanupOldMonitoring();

    // Clean up old timer files (older than 1 hour)
    cleanupOldTimerFiles();

    echo "[" . date('Y-m-d H:i:s') . "] QR cleanup completed successfully.\n";

} catch (Exception $e) {
    echo "[" . date('Y-m-d H:i:s') . "] QR cleanup error: " . $e->getMessage() . "\n";
    error_log("QR cleanup cron error: " . $e->getMessage());
}

// Clean up old monitoring records
function cleanupOldMonitoring() {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE started_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        
        if ($deleted > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] Cleaned up $deleted old monitoring records.\n";
        }
        
    } catch (Exception $e) {
        error_log("Cleanup old monitoring error: " . $e->getMessage());
    }
}

// Clean up old timer files
function cleanupOldTimerFiles() {
    try {
        $timer_files = glob(sys_get_temp_dir() . "/qr_timer_*.json");
        $deleted = 0;

        foreach ($timer_files as $timer_file) {
            // Delete timer files older than 1 hour
            if (filemtime($timer_file) < time() - 3600) {
                unlink($timer_file);
                $deleted++;
            }
        }

        if ($deleted > 0) {
            echo "[" . date('Y-m-d H:i:s') . "] Cleaned up $deleted old timer files.\n";
        }

    } catch (Exception $e) {
        error_log("Cleanup old timer files error: " . $e->getMessage());
    }
}

// Also check for payments that might have been missed
function checkMissedPayments() {
    global $pdo;
    
    try {
        // Get pending transactions older than 5 minutes
        $stmt = $pdo->prepare("
            SELECT bt.*, pm.check_count 
            FROM bot_transactions bt
            LEFT JOIN payment_monitoring pm ON bt.chat_id = pm.chat_id AND bt.md5 = pm.md5
            WHERE bt.status = 'pending' 
            AND bt.created_at < DATE_SUB(NOW(), INTERVAL 5 MINUTE)
            AND bt.created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
        ");
        $stmt->execute();
        $transactions = $stmt->fetchAll();
        
        foreach ($transactions as $transaction) {
            // Do a final check with Bakong API
            if (checkPaymentStatusAPI($transaction['chat_id'], $transaction['md5'], $transaction['amount'])) {
                echo "[" . date('Y-m-d H:i:s') . "] Found missed payment for chat_id: {$transaction['chat_id']}\n";
            }
        }
        
    } catch (Exception $e) {
        error_log("Check missed payments error: " . $e->getMessage());
    }
}

echo "[" . date('Y-m-d H:i:s') . "] QR cleanup process finished.\n";
?>
