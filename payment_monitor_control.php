<!DOCTYPE html>
<html>
<head>
    <title>Payment Monitor Control</title>
    <meta http-equiv="refresh" content="5">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .running { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .stopped { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .button { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; }
        .start { background: #28a745; color: white; }
        .stop { background: #dc3545; color: white; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>
    <h2>🤖 Auto Payment Monitor Control</h2>
    
    <?php
    require_once 'config.php';
    
    // Handle start/stop actions
    if (isset($_GET['action'])) {
        if ($_GET['action'] === 'start') {
            // Start the monitor in background
            if (PHP_OS_FAMILY === 'Windows') {
                $command = 'start /B php "' . __DIR__ . '/auto_payment_monitor.php"';
            } else {
                $command = 'php "' . __DIR__ . '/auto_payment_monitor.php" > /dev/null 2>&1 &';
            }
            exec($command);
            echo "<div class='status running'>✅ Payment monitor started!</div>";
        } elseif ($_GET['action'] === 'stop') {
            // Kill PHP processes running the monitor
            if (PHP_OS_FAMILY === 'Windows') {
                exec('taskkill /F /IM php.exe');
            } else {
                exec('pkill -f auto_payment_monitor.php');
            }
            echo "<div class='status stopped'>⏹️ Payment monitor stopped!</div>";
        }
    }
    
    // Check if monitor is running
    $is_running = false;
    if (PHP_OS_FAMILY === 'Windows') {
        $output = shell_exec('tasklist /FI "IMAGENAME eq php.exe" 2>NUL');
        $is_running = strpos($output, 'php.exe') !== false;
    } else {
        $output = shell_exec('pgrep -f auto_payment_monitor.php');
        $is_running = !empty(trim($output));
    }
    
    echo "<div class='status " . ($is_running ? 'running' : 'stopped') . "'>";
    echo $is_running ? "🟢 Payment Monitor: RUNNING" : "🔴 Payment Monitor: STOPPED";
    echo "</div>";
    
    echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";
    
    if ($is_running) {
        echo "<a href='?action=stop' class='button stop'>⏹️ Stop Monitor</a>";
    } else {
        echo "<a href='?action=start' class='button start'>▶️ Start Monitor</a>";
    }
    
    // Show pending transactions
    try {
        $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE status = 'pending' ORDER BY created_at DESC LIMIT 10");
        $stmt->execute();
        $pending = $stmt->fetchAll();
        
        echo "<h3>📋 Pending Payments (" . count($pending) . ")</h3>";
        
        if ($pending) {
            echo "<table>";
            echo "<tr><th>Chat ID</th><th>Amount</th><th>MD5</th><th>Created</th><th>Age</th></tr>";
            
            foreach ($pending as $tx) {
                $age_minutes = (time() - strtotime($tx['created_at'])) / 60;
                $age_color = $age_minutes > 3 ? 'red' : 'green';
                
                echo "<tr>";
                echo "<td>{$tx['chat_id']}</td>";
                echo "<td>{$tx['amount']} USD</td>";
                echo "<td>" . substr($tx['md5'], 0, 8) . "...</td>";
                echo "<td>{$tx['created_at']}</td>";
                echo "<td style='color: $age_color;'>" . round($age_minutes, 1) . " min</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>✅ No pending payments</p>";
        }
        
        // Show recent completed payments
        $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE status = 'completed' ORDER BY created_at DESC LIMIT 5");
        $stmt->execute();
        $completed = $stmt->fetchAll();
        
        echo "<h3>✅ Recent Completed Payments</h3>";
        
        if ($completed) {
            echo "<table>";
            echo "<tr><th>Chat ID</th><th>Amount</th><th>Completed</th></tr>";
            
            foreach ($completed as $tx) {
                echo "<tr>";
                echo "<td>{$tx['chat_id']}</td>";
                echo "<td>{$tx['amount']} USD</td>";
                echo "<td>{$tx['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No completed payments yet</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    }
    ?>
    
    <div style="background: #e8f5e8; padding: 15px; border-left: 4px solid green; margin: 20px 0;">
        <h4>🎯 How Auto Payment Monitor Works:</h4>
        <ul>
            <li>⏰ <strong>Checks every 2 seconds</strong> - Continuously monitors all pending payments</li>
            <li>🔍 <strong>No user interaction needed</strong> - Automatically detects completed payments</li>
            <li>✅ <strong>Instant confirmation</strong> - Sends confirmation message immediately</li>
            <li>💰 <strong>Auto balance update</strong> - Updates user balance automatically</li>
            <li>🗑️ <strong>QR cleanup</strong> - Deletes QR message after payment</li>
        </ul>
    </div>
    
    <div style="background: #fff3cd; padding: 15px; border-left: 4px solid orange; margin: 20px 0;">
        <h4>📝 Instructions:</h4>
        <ol>
            <li><strong>Start Monitor:</strong> Click "Start Monitor" button above</li>
            <li><strong>Keep Running:</strong> Keep this page open or run monitor in background</li>
            <li><strong>Test Payment:</strong> Generate QR with /topup and pay it</li>
            <li><strong>Auto Confirmation:</strong> Payment will be confirmed automatically within 2 seconds</li>
        </ol>
    </div>
    
</body>
</html>
