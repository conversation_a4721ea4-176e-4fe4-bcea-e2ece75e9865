<?php
/**
 * Auto Payment Monitor - Continuously checks for completed payments
 * Runs every 2 seconds to check all pending payments
 * No user interaction required - fully automatic
 */

require_once 'config.php';

echo "🤖 Auto Payment Monitor Started\n";
echo "Checking payments every 2 seconds...\n";
echo "Press Ctrl+C to stop\n\n";

// Run forever
while (true) {
    try {
        // Get all pending transactions
        $stmt = $pdo->prepare("
            SELECT * FROM bot_transactions 
            WHERE status = 'pending' 
            ORDER BY created_at DESC
        ");
        $stmt->execute();
        $pending_transactions = $stmt->fetchAll();
        
        if (count($pending_transactions) > 0) {
            echo "[" . date('H:i:s') . "] Checking " . count($pending_transactions) . " pending payment(s)...\n";
            
            foreach ($pending_transactions as $transaction) {
                $chat_id = $transaction['chat_id'];
                $md5 = $transaction['md5'];
                $amount = $transaction['amount'];
                $message_id = $transaction['message_id'];
                
                // Check payment status via API
                $api_url = "https://api.kunchhunlichhean.org/khqr/check?md5=" . urlencode($md5);
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $api_url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                curl_setopt($ch, CURLOPT_TIMEOUT, 5);
                
                $response = curl_exec($ch);
                $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($http_code == 200) {
                    $response_data = json_decode($response, true);
                    
                    if (isset($response_data['responseCode']) && $response_data['responseCode'] == 0) {
                        // PAYMENT FOUND!
                        echo "  ✅ PAYMENT DETECTED! chat_id: $chat_id, amount: $amount USD\n";
                        
                        // Mark as completed to prevent duplicates
                        $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'completed' WHERE chat_id = ? AND md5 = ? AND status = 'pending'");
                        $stmt->execute([$chat_id, $md5]);
                        
                        if ($stmt->rowCount() > 0) {
                            // Update user balance
                            $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE chat_id = ?");
                            $stmt->execute([$amount, $chat_id]);
                            
                            // Send success message via Telegram API
                            $bot_token = TELEGRAM_BOT_TOKEN;
                            $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
                            
                            $data = [
                                'chat_id' => $chat_id,
                                'text' => "✅ Payment of {$amount} USD confirmed!\n💰 Balance updated automatically!",
                                'parse_mode' => 'HTML'
                            ];
                            
                            $ch = curl_init();
                            curl_setopt($ch, CURLOPT_URL, $api_url);
                            curl_setopt($ch, CURLOPT_POST, true);
                            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                            
                            curl_exec($ch);
                            curl_close($ch);
                            
                            // Delete QR message
                            if ($message_id) {
                                $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
                                $data = [
                                    'chat_id' => $chat_id,
                                    'message_id' => $message_id
                                ];
                                
                                $ch = curl_init();
                                curl_setopt($ch, CURLOPT_URL, $api_url);
                                curl_setopt($ch, CURLOPT_POST, true);
                                curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
                                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                                curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
                                
                                curl_exec($ch);
                                curl_close($ch);
                                
                                echo "  🗑️ QR message deleted\n";
                            }
                            
                            // Remove from payment monitoring
                            $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
                            $stmt->execute([$chat_id, $md5]);
                            
                            echo "  💰 Balance updated for chat_id: $chat_id\n";
                        }
                    }
                }
            }
        }
        
        // Wait 2 seconds before next check
        sleep(2);
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
        sleep(2); // Wait before retrying
    }
}
?>
