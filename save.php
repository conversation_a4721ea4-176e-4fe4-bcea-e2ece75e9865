<?php
// Connect to the database
include 'config.php';

// Enable error reporting
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

// Establish database connection if not already done
if (!isset($conn) || !$conn) {
    try {
        $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    } catch (mysqli_sql_exception $e) {
        // Log the connection error
        error_log("Database Connection Error: " . $e->getMessage());
        die("Connection failed: " . $e->getMessage());
    }
}

// Check connection
if (!$conn) {
    error_log("Database connection failed: " . mysqli_connect_error());
    die("Connection failed: " . mysqli_connect_error());
}

// Get the parameters from the URL
$qrData = mysqli_real_escape_string($conn, $_GET['qr']);
$amount = mysqli_real_escape_string($conn, $_GET['amount']);
$md5 = mysqli_real_escape_string($conn, $_GET['md5']);
$username = mysqli_real_escape_string($conn, $_GET['username']);

// Prepare the SQL query
$sql = "INSERT INTO qrcode_data (qr_data, amount, md5, username, status) VALUES ('$qrData', '$amount', '$md5', '$username', 'Pending')";

try {
    // Execute the query
    $result = mysqli_query($conn, $sql);

    if ($result) {
        // Redirect to qrcode.php with the parameters
        header("Location: qrcode.php?qr=" . urlencode($qrData) . 
                               "&amount=" . urlencode($amount) . 
                               "&md5=" . urlencode($md5) . 
                               "&username=" . urlencode($username));
        exit;
    } else {
        // Log the error
        error_log("Query Error: " . mysqli_error($conn));
        error_log("Failed SQL: " . $sql);
        die("Error inserting data: " . mysqli_error($conn));
    }
} catch (mysqli_sql_exception $e) {
    // Log any SQL exceptions
    error_log("SQL Exception: " . $e->getMessage());
    die("An error occurred: " . $e->getMessage());
} finally {
    // Close the database connection
    mysqli_close($conn);
}
?>