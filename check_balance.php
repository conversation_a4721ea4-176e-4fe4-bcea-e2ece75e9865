<?php
require_once 'config.php';

echo "<h2>Balance Check</h2>";

try {
    // Check user balance
    $chat_id = 1630035459; // Your Telegram ID
    
    $stmt = $pdo->prepare("SELECT id, balance, username FROM telegram_users WHERE telegram_id = ?");
    $stmt->execute([$chat_id]);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "<p><strong>User ID:</strong> {$user['id']}</p>";
        echo "<p><strong>Username:</strong> {$user['username']}</p>";
        echo "<p><strong>Current Balance:</strong> {$user['balance']} USD</p>";
        
        // Check recent transactions
        echo "<h3>Recent Balance Transactions:</h3>";
        $stmt = $pdo->prepare("
            SELECT * FROM balance_transactions 
            WHERE telegram_user_id = ? 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$user['id']]);
        $transactions = $stmt->fetchAll();
        
        if ($transactions) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>Type</th><th>Amount</th><th>Description</th><th>Date</th></tr>";
            foreach ($transactions as $tx) {
                echo "<tr>";
                echo "<td>{$tx['id']}</td>";
                echo "<td>{$tx['transaction_type']}</td>";
                echo "<td>{$tx['amount']}</td>";
                echo "<td>{$tx['description']}</td>";
                echo "<td>{$tx['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No transactions found.</p>";
        }
        
        // Check bot transactions
        echo "<h3>Recent Bot Transactions:</h3>";
        $stmt = $pdo->prepare("
            SELECT * FROM bot_transactions 
            WHERE chat_id = ? 
            ORDER BY created_at DESC 
            LIMIT 10
        ");
        $stmt->execute([$chat_id]);
        $bot_transactions = $stmt->fetchAll();
        
        if ($bot_transactions) {
            echo "<table border='1'>";
            echo "<tr><th>ID</th><th>MD5</th><th>Amount</th><th>Status</th><th>Date</th></tr>";
            foreach ($bot_transactions as $tx) {
                echo "<tr>";
                echo "<td>{$tx['id']}</td>";
                echo "<td>{$tx['md5']}</td>";
                echo "<td>{$tx['amount']}</td>";
                echo "<td>{$tx['status']}</td>";
                echo "<td>{$tx['created_at']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No bot transactions found.</p>";
        }
        
    } else {
        echo "<p>User not found for chat_id: $chat_id</p>";
    }
    
} catch (Exception $e) {
    echo "<p>Error: " . $e->getMessage() . "</p>";
}
?>
