<?php
/**
 * Background QR Deletion Script
 * 
 * This script runs in the background and deletes QR codes after 3 minutes
 * Usage: php delete_qr_background.php <chat_id> <md5>
 */

require_once 'config.php';

// Get parameters from command line
$chat_id = $argv[1] ?? null;
$md5 = $argv[2] ?? null;

if (!$chat_id || !$md5) {
    error_log("Background QR deletion: Missing parameters");
    exit(1);
}

error_log("Background QR deletion started for chat_id: $chat_id, md5: $md5");

// Wait for 3 minutes (180 seconds)
sleep(180);

try {
    $bot = new TelegramBot(TELEGRAM_BOT_TOKEN);
    
    // Check if transaction is still pending
    $stmt = $pdo->prepare("SELECT message_id, status FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
    $stmt->execute([$chat_id, $md5]);
    $transaction = $stmt->fetch();
    
    if ($transaction && $transaction['status'] === 'pending') {
        error_log("Background QR deletion: Deleting expired QR for chat_id: $chat_id");
        
        // Delete the QR code message from Telegram
        if ($transaction['message_id']) {
            try {
                $bot->deleteMessage($chat_id, $transaction['message_id']);
                error_log("Background QR deletion: Message deleted successfully");
            } catch (Exception $e) {
                error_log("Background QR deletion: Failed to delete message - " . $e->getMessage());
            }
        }
        
        // Update transaction status to expired
        $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        
        // Remove from payment monitoring
        $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        
        // Send expiration message via direct API call
        sendDirectMessage($chat_id, "⏰ QR code expired. Please generate a new one if needed.");
        
        error_log("Background QR deletion: Process completed for chat_id: $chat_id");
    } else {
        error_log("Background QR deletion: Transaction already processed or not found");
    }
    
} catch (Exception $e) {
    error_log("Background QR deletion error: " . $e->getMessage());
}

error_log("Background QR deletion finished for chat_id: $chat_id, md5: $md5");

// Direct API call function to avoid webhook loops
function sendDirectMessage($chat_id, $text) {
    $bot_token = TELEGRAM_BOT_TOKEN;
    $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";

    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    curl_close($ch);

    error_log("Direct message sent: $text");
}
?>
