<?php
require_once '../config.php';

if (!is_admin_logged_in()) {
    redirect('../index.php');
}

$message = '';
$accounts = [];

// Handle account actions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['delete_account'])) {
        $account_id = (int)$_POST['account_id'];
        try {
            $stmt = $pdo->prepare("DELETE FROM accounts WHERE id = ?");
            $stmt->execute([$account_id]);
            $message = "✅ Account deleted successfully.";
        } catch (PDOException $e) {
            $message = "❌ Error deleting account: " . $e->getMessage();
        }
    }
}

// Get all accounts
try {
    $stmt = $pdo->query("SELECT * FROM accounts ORDER BY created_at DESC");
    $accounts = $stmt->fetchAll();
} catch (PDOException $e) {
    $message = "❌ Error loading accounts: " . $e->getMessage();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Accounts - Admin Panel</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #3b82f6;
            --secondary: #8b5cf6;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --sidebar-bg: #1e293b;
            --card-bg: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border: #e5e7eb;
            --hover: #f3f4f6;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            color: var(--text-primary);
            line-height: 1.6;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: var(--sidebar-bg);
            z-index: 1000;
            overflow-y: auto;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            color: white;
            text-decoration: none;
        }
        
        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
        }
        
        .logo-text {
            font-size: 1.25rem;
            font-weight: 700;
        }
        
        .nav-menu {
            padding: 1rem 0;
        }
        
        .nav-item {
            margin: 0.25rem 1rem;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            color: #cbd5e1;
            text-decoration: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            background: rgba(59, 130, 246, 0.1);
            color: #60a5fa;
            transform: translateX(4px);
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
            font-size: 1.1rem;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .top-bar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .add-btn {
            background: linear-gradient(135deg, var(--success), #047857);
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 16px rgba(16, 185, 129, 0.3);
        }
        
        /* Content */
        .content {
            padding: 2rem;
        }
        
        .alert {
            padding: 1rem 1.5rem;
            border-radius: 12px;
            margin-bottom: 2rem;
            font-weight: 500;
        }
        
        .alert-success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        /* Accounts Grid */
        .accounts-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 1.5rem;
        }
        
        .account-card {
            background: white;
            border-radius: 16px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .account-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary), var(--secondary));
        }
        
        .account-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }
        
        .account-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .account-code {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        
        .status-available {
            background: #d1fae5;
            color: #065f46;
        }
        
        .status-sold {
            background: #fee2e2;
            color: #991b1b;
        }

        .status-unknown {
            background: #f3f4f6;
            color: #6b7280;
        }
        
        .account-details {
            margin-bottom: 1.5rem;
        }
        
        .detail-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }
        
        .detail-label {
            color: var(--text-secondary);
            font-weight: 500;
        }
        
        .detail-value {
            color: var(--text-primary);
            font-weight: 600;
        }
        
        .price {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--success);
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .account-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .btn {
            flex: 1;
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            font-size: 0.875rem;
        }
        
        .btn-edit {
            background: var(--primary);
            color: white;
        }
        
        .btn-edit:hover {
            background: #2563eb;
        }
        
        .btn-delete {
            background: var(--danger);
            color: white;
        }
        
        .btn-delete:hover {
            background: #dc2626;
        }
        
        .empty-state {
            text-align: center;
            padding: 4rem 2rem;
            color: var(--text-secondary);
        }
        
        .empty-state i {
            font-size: 4rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .accounts-grid {
                grid-template-columns: 1fr;
            }
            
            .top-bar {
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <a href="dashboard.php" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-crown"></i>
                </div>
                <div class="logo-text">Admin Panel</div>
            </a>
        </div>
        
        <nav class="nav-menu">
            <div class="nav-item">
                <a href="dashboard.php" class="nav-link">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
            </div>
            <div class="nav-item">
                <a href="accounts.php" class="nav-link active">
                    <i class="fas fa-gamepad"></i>
                    Accounts
                </a>
            </div>
            <div class="nav-item">
                <a href="add_account.php" class="nav-link">
                    <i class="fas fa-plus-circle"></i>
                    Add Account
                </a>
            </div>
            <div class="nav-item">
                <a href="purchases.php" class="nav-link">
                    <i class="fas fa-shopping-cart"></i>
                    Purchases
                </a>
            </div>
            <div class="nav-item">
                <a href="balance_management.php" class="nav-link">
                    <i class="fas fa-wallet"></i>
                    Balance Management
                </a>
            </div>
            <div class="nav-item">
                <a href="topup_settings.php" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Top-Up Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="telegram_users.php" class="nav-link">
                    <i class="fab fa-telegram"></i>
                    Telegram Users
                </a>
            </div>
            <div class="nav-item">
                <a href="bot_webhook.php" class="nav-link">
                    <i class="fas fa-robot"></i>
                    Bot Settings
                </a>
            </div>
            <div class="nav-item">
                <a href="security_lockouts.php" class="nav-link">
                    <i class="fas fa-shield-alt"></i>
                    Security
                </a>
            </div>
        </nav>
    </div>
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="top-bar">
            <h1 class="page-title">Manage Accounts</h1>
            <a href="add_account.php" class="add-btn">
                <i class="fas fa-plus"></i>
                Add New Account
            </a>
        </div>
        
        <div class="content">
            <?php if ($message): ?>
                <div class="alert <?php echo strpos($message, '✅') !== false ? 'alert-success' : 'alert-error'; ?>">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>
            
            <?php if (empty($accounts)): ?>
                <div class="empty-state">
                    <i class="fas fa-gamepad"></i>
                    <h3>No accounts found</h3>
                    <p>Start by adding your first gaming account to the system.</p>
                    <a href="add_account.php" class="add-btn" style="margin-top: 1rem; display: inline-flex;">
                        <i class="fas fa-plus"></i>
                        Add First Account
                    </a>
                </div>
            <?php else: ?>
                <div class="accounts-grid">
                    <?php foreach ($accounts as $account): ?>
                        <div class="account-card">
                            <div class="account-header">
                                <div class="account-code">#<?php echo htmlspecialchars($account['account_code'] ?? 'N/A'); ?></div>
                                <div class="status-badge status-<?php echo $account['status'] ?? 'unknown'; ?>">
                                    <?php echo ucfirst($account['status'] ?? 'unknown'); ?>
                                </div>
                            </div>
                            
                            <div class="account-details">
                                <div class="detail-row">
                                    <span class="detail-label">Game:</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($account['game_type'] ?? 'Unknown'); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Status:</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($account['account_status'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Rank:</span>
                                    <span class="detail-value"><?php echo htmlspecialchars($account['rank_tier'] ?? 'N/A'); ?></span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Created:</span>
                                    <span class="detail-value"><?php echo $account['created_at'] ? date('M j, Y', strtotime($account['created_at'])) : 'Unknown'; ?></span>
                                </div>
                            </div>
                            
                            <div class="price">$<?php echo number_format($account['price'] ?? 0, 2); ?></div>
                            
                            <div class="account-actions">
                                <a href="edit_account.php?id=<?php echo $account['id']; ?>" class="btn btn-edit">
                                    <i class="fas fa-edit"></i> Edit
                                </a>
                                <form method="POST" style="flex: 1;" onsubmit="return confirm('Are you sure you want to delete this account?')">
                                    <input type="hidden" name="account_id" value="<?php echo $account['id']; ?>">
                                    <button type="submit" name="delete_account" class="btn btn-delete" style="width: 100%;">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
