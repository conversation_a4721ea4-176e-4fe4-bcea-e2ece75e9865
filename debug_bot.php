<?php
require_once 'config.php';

echo "<h1>🔍 Bot Debug & Troubleshooting</h1>";

// 1. Check bot token
echo "<h2>1. 🤖 Bot Token Test</h2>";
$bot_url = TELEGRAM_API_URL . 'getMe';
$bot_result = file_get_contents($bot_url);
$bot_response = json_decode($bot_result, true);

if ($bot_response && $bot_response['ok']) {
    echo "<div style='color: green; background: #d4edda; padding: 1rem; border-radius: 5px;'>";
    echo "✅ Bot token is working<br>";
    echo "Bot Name: " . htmlspecialchars($bot_response['result']['first_name']) . "<br>";
    echo "Username: @" . htmlspecialchars($bot_response['result']['username']) . "<br>";
    echo "</div>";
} else {
    echo "<div style='color: red; background: #f8d7da; padding: 1rem; border-radius: 5px;'>";
    echo "❌ Bot token error: " . htmlspecialchars($bot_response['description'] ?? 'Unknown error') . "<br>";
    echo "</div>";
}

// 2. Check webhook status
echo "<h2>2. 📡 Webhook Status</h2>";
$webhook_url = TELEGRAM_API_URL . 'getWebhookInfo';
$webhook_result = file_get_contents($webhook_url);
$webhook_response = json_decode($webhook_result, true);

if ($webhook_response && $webhook_response['ok']) {
    $webhook_info = $webhook_response['result'];
    echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px;'>";
    echo "<strong>Webhook URL:</strong> " . htmlspecialchars($webhook_info['url'] ?: 'Not set') . "<br>";
    echo "<strong>Pending updates:</strong> " . $webhook_info['pending_update_count'] . "<br>";
    
    if (isset($webhook_info['last_error_date'])) {
        echo "<div style='color: red; margin-top: 0.5rem;'>";
        echo "<strong>❌ Last Error:</strong> " . date('Y-m-d H:i:s', $webhook_info['last_error_date']) . "<br>";
        echo "<strong>Error Message:</strong> " . htmlspecialchars($webhook_info['last_error_message']) . "<br>";
        echo "</div>";
    } else {
        echo "<div style='color: green; margin-top: 0.5rem;'>";
        echo "✅ No webhook errors<br>";
        echo "</div>";
    }
    echo "</div>";
}

// 3. Test webhook URL accessibility
echo "<h2>3. 🌐 Webhook URL Test</h2>";
$test_webhook_url = SITE_URL . '/telegram_bot.php';
echo "<p><strong>Testing URL:</strong> " . htmlspecialchars($test_webhook_url) . "</p>";

$headers = @get_headers($test_webhook_url);
if ($headers) {
    if (strpos($headers[0], '200') !== false || strpos($headers[0], '405') !== false) {
        echo "<div style='color: green; background: #d4edda; padding: 1rem; border-radius: 5px;'>";
        echo "✅ Webhook URL is accessible<br>";
        echo "Response: " . htmlspecialchars($headers[0]) . "<br>";
        echo "</div>";
    } else {
        echo "<div style='color: red; background: #f8d7da; padding: 1rem; border-radius: 5px;'>";
        echo "❌ Webhook URL returned: " . htmlspecialchars($headers[0]) . "<br>";
        echo "</div>";
    }
} else {
    echo "<div style='color: red; background: #f8d7da; padding: 1rem; border-radius: 5px;'>";
    echo "❌ Cannot reach webhook URL<br>";
    echo "</div>";
}

// 4. Check database connection
echo "<h2>4. 🗄️ Database Connection</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM telegram_users");
    $user_count = $stmt->fetchColumn();
    echo "<div style='color: green; background: #d4edda; padding: 1rem; border-radius: 5px;'>";
    echo "✅ Database connection working<br>";
    echo "Telegram users in database: " . $user_count . "<br>";
    echo "</div>";
} catch (PDOException $e) {
    echo "<div style='color: red; background: #f8d7da; padding: 1rem; border-radius: 5px;'>";
    echo "❌ Database error: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "</div>";
}

// 5. Check recent updates
echo "<h2>5. 📨 Recent Updates</h2>";
$updates_url = TELEGRAM_API_URL . 'getUpdates?limit=5';
$updates_result = file_get_contents($updates_url);
$updates_response = json_decode($updates_result, true);

if ($updates_response && $updates_response['ok']) {
    if (!empty($updates_response['result'])) {
        echo "<div style='background: #fff3cd; padding: 1rem; border-radius: 5px;'>";
        echo "<strong>⚠️ Found " . count($updates_response['result']) . " recent update(s)</strong><br>";
        echo "This might mean webhook is not working properly.<br>";
        echo "Recent messages:<br>";
        foreach ($updates_response['result'] as $update) {
            if (isset($update['message'])) {
                $msg = $update['message'];
                echo "- " . htmlspecialchars($msg['text'] ?? 'No text') . " from " . htmlspecialchars($msg['from']['first_name'] ?? 'Unknown') . "<br>";
            }
        }
        echo "</div>";
    } else {
        echo "<div style='color: green; background: #d4edda; padding: 1rem; border-radius: 5px;'>";
        echo "✅ No pending updates (webhook working properly)<br>";
        echo "</div>";
    }
}

// 6. Test sending a message
echo "<h2>6. 📤 Test Send Message</h2>";
if (!empty($updates_response['result'])) {
    $latest_update = end($updates_response['result']);
    if (isset($latest_update['message']['chat']['id'])) {
        $test_chat_id = $latest_update['message']['chat']['id'];
        
        $test_message_url = TELEGRAM_API_URL . 'sendMessage';
        $test_data = [
            'chat_id' => $test_chat_id,
            'text' => "🧪 <b>Bot Test Message</b>\n\nThis is a test to see if the bot can send messages.\nTime: " . date('Y-m-d H:i:s'),
            'parse_mode' => 'HTML'
        ];
        
        $test_options = [
            'http' => [
                'header' => "Content-type: application/x-www-form-urlencoded\r\n",
                'method' => 'POST',
                'content' => http_build_query($test_data)
            ]
        ];
        
        $test_context = stream_context_create($test_options);
        $test_result = file_get_contents($test_message_url, false, $test_context);
        $test_response = json_decode($test_result, true);
        
        if ($test_response && $test_response['ok']) {
            echo "<div style='color: green; background: #d4edda; padding: 1rem; border-radius: 5px;'>";
            echo "✅ Test message sent successfully!<br>";
            echo "Check your Telegram chat - you should see a test message.<br>";
            echo "</div>";
        } else {
            echo "<div style='color: red; background: #f8d7da; padding: 1rem; border-radius: 5px;'>";
            echo "❌ Failed to send test message<br>";
            echo "Error: " . htmlspecialchars($test_response['description'] ?? 'Unknown error') . "<br>";
            echo "</div>";
        }
    }
} else {
    echo "<div style='background: #e7f3ff; padding: 1rem; border-radius: 5px;'>";
    echo "ℹ️ No recent messages to test with. Send a message to your bot first.<br>";
    echo "</div>";
}

// 7. Configuration check
echo "<h2>7. ⚙️ Configuration</h2>";
echo "<div style='background: #f8f9fa; padding: 1rem; border-radius: 5px;'>";
echo "<strong>SITE_URL:</strong> " . htmlspecialchars(SITE_URL) . "<br>";
echo "<strong>Bot Token:</strong> " . htmlspecialchars(substr(TELEGRAM_BOT_TOKEN, 0, 10)) . "...<br>";
echo "<strong>Webhook URL:</strong> " . htmlspecialchars(SITE_URL . '/telegram_bot.php') . "<br>";
echo "</div>";

// Quick fixes
echo "<h2>🔧 Quick Fixes</h2>";
echo "<div style='background: #fff3cd; padding: 1rem; border-radius: 5px;'>";
echo "<h3>Try these solutions:</h3>";
echo "<ol>";
echo "<li><strong>Reset webhook:</strong> <a href='update_webhook.php'>Click here to reset webhook</a></li>";
echo "<li><strong>Clear pending updates:</strong> <a href='?clear_updates=1'>Click here to clear pending updates</a></li>";
echo "<li><strong>Check ngrok:</strong> Make sure ngrok is still running</li>";
echo "<li><strong>Test webhook file:</strong> <a href='telegram_bot.php'>Click here to test webhook file</a></li>";
echo "</ol>";
echo "</div>";

// Handle clear updates
if (isset($_GET['clear_updates'])) {
    $clear_url = TELEGRAM_API_URL . 'getUpdates?offset=-1';
    file_get_contents($clear_url);
    echo "<div style='color: green; background: #d4edda; padding: 1rem; border-radius: 5px; margin-top: 1rem;'>";
    echo "✅ Pending updates cleared. Try sending /start to your bot now.<br>";
    echo "</div>";
}
?>
