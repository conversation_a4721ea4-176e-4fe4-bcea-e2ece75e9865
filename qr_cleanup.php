<?php
require_once 'config.php';

// Simple QR cleanup - check for expired QR codes and delete them

try {
    // Find all pending QR transactions older than 3 minutes
    $stmt = $pdo->prepare("
        SELECT * FROM bot_transactions 
        WHERE status = 'pending' 
        AND created_at <= DATE_SUB(NOW(), INTERVAL 3 MINUTE)
        ORDER BY created_at ASC
    ");
    $stmt->execute();
    $expired_transactions = $stmt->fetchAll();
    
    foreach ($expired_transactions as $transaction) {
        $chat_id = $transaction['chat_id'];
        $md5 = $transaction['md5'];
        $message_id = $transaction['message_id'];
        
        error_log("QR cleanup: Processing expired QR - chat_id: $chat_id, md5: $md5, message_id: $message_id");
        
        // Delete QR message from Telegram
        if ($message_id) {
            $bot_token = TELEGRAM_BOT_TOKEN;
            $api_url = "https://api.telegram.org/bot{$bot_token}/deleteMessage";
            
            $data = [
                'chat_id' => $chat_id,
                'message_id' => $message_id
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($http_code == 200) {
                error_log("QR cleanup: Message $message_id deleted successfully");
            } else {
                error_log("QR cleanup: Failed to delete message $message_id - HTTP $http_code - $response");
            }
        }
        
        // Update transaction status to expired
        $stmt = $pdo->prepare("UPDATE bot_transactions SET status = 'expired' WHERE id = ?");
        $stmt->execute([$transaction['id']]);
        
        // Remove from payment monitoring
        $stmt = $pdo->prepare("DELETE FROM payment_monitoring WHERE chat_id = ? AND md5 = ?");
        $stmt->execute([$chat_id, $md5]);
        
        // Send expiration message
        $bot_token = TELEGRAM_BOT_TOKEN;
        $api_url = "https://api.telegram.org/bot{$bot_token}/sendMessage";
        
        $data = [
            'chat_id' => $chat_id,
            'text' => "⏰ QR code expired. Please generate a new one if needed."
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        curl_exec($ch);
        curl_close($ch);
        
        error_log("QR cleanup: Expiration message sent for chat_id: $chat_id");
    }
    
    if (count($expired_transactions) > 0) {
        error_log("QR cleanup: Processed " . count($expired_transactions) . " expired QR codes");
    }
    
} catch (Exception $e) {
    error_log("QR cleanup error: " . $e->getMessage());
}
?>
