<?php
require_once 'config.php';

$chat_id = $_GET['chat_id'] ?? null;
$md5 = $_GET['md5'] ?? null;
$amount = $_GET['amount'] ?? null;
$message_id = $_GET['message_id'] ?? null;

if (!$chat_id || !$md5 || !$amount) {
    exit('Missing parameters');
}

// Check if transaction is still pending
$stmt = $pdo->prepare("SELECT status FROM bot_transactions WHERE chat_id = ? AND md5 = ?");
$stmt->execute([$chat_id, $md5]);
$transaction = $stmt->fetch();

if (!$transaction || $transaction['status'] !== 'pending') {
    exit('Transaction not found or already processed');
}
?>
<!DOCTYPE html>
<html>
<head>
    <title>Auto Payment Check</title>
    <meta charset="utf-8">
</head>
<body>
    <div id="status">🔄 Checking payment automatically...</div>
    
    <script>
        let checkCount = 0;
        const maxChecks = 180; // 3 minutes (180 seconds)
        
        function checkPayment() {
            checkCount++;
            
            if (checkCount > maxChecks) {
                document.getElementById('status').innerHTML = '⏰ Payment check timeout';
                return;
            }
            
            // Update status
            document.getElementById('status').innerHTML = `🔄 Checking payment... (${checkCount}s)`;
            
            // Make API call to check payment
            fetch('https://api.kunchhunlichhean.org/khqr/check?md5=<?php echo urlencode($md5); ?>')
                .then(response => response.json())
                .then(data => {
                    if (data.responseCode === 0) {
                        // Payment found! Process it
                        document.getElementById('status').innerHTML = '✅ Payment detected! Processing...';
                        
                        // Call our processing endpoint
                        fetch('process_payment.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: `chat_id=<?php echo urlencode($chat_id); ?>&md5=<?php echo urlencode($md5); ?>&amount=<?php echo urlencode($amount); ?>&message_id=<?php echo urlencode($message_id); ?>`
                        })
                        .then(response => response.text())
                        .then(result => {
                            document.getElementById('status').innerHTML = '✅ Payment confirmed! You can close this page.';
                        })
                        .catch(error => {
                            console.error('Processing error:', error);
                            document.getElementById('status').innerHTML = '❌ Error processing payment';
                        });
                        
                    } else {
                        // Payment not found yet, continue checking
                        setTimeout(checkPayment, 1000); // Check again in 1 second
                    }
                })
                .catch(error => {
                    console.error('Check error:', error);
                    // Continue checking even if there's an error
                    setTimeout(checkPayment, 1000);
                });
        }
        
        // Start checking immediately
        checkPayment();
    </script>
</body>
</html>
