<?php
require_once 'config.php';

echo "<h2>FORCE Balance Fix</h2>";

$chat_id = 1630035459; // Your Telegram ID

try {
    // Get user info
    $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
    $stmt->execute([$chat_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "<p>❌ User not found</p>";
        exit;
    }
    
    echo "<p><strong>User ID:</strong> {$user['id']}</p>";
    echo "<p><strong>Current Balance:</strong> {$user['balance']} USD</p>";
    
    // Get completed bot transactions
    $stmt = $pdo->prepare("SELECT * FROM bot_transactions WHERE chat_id = ? AND status = 'completed' ORDER BY created_at DESC");
    $stmt->execute([$chat_id]);
    $completed_transactions = $stmt->fetchAll();
    
    echo "<h3>Completed Bot Transactions:</h3>";
    $total_should_be = 0;
    
    if ($completed_transactions) {
        echo "<table border='1'>";
        echo "<tr><th>MD5</th><th>Amount</th><th>Date</th><th>Balance Updated?</th></tr>";
        
        foreach ($completed_transactions as $tx) {
            $total_should_be += $tx['amount'];
            
            // Check if balance was updated for this transaction
            $stmt = $pdo->prepare("SELECT id FROM balance_transactions WHERE description LIKE ? AND telegram_user_id = ?");
            $stmt->execute(["%{$tx['md5']}%", $user['id']]);
            $balance_updated = $stmt->fetch() ? "✅ Yes" : "❌ No";
            
            echo "<tr>";
            echo "<td>{$tx['md5']}</td>";
            echo "<td>{$tx['amount']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "<td>$balance_updated</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><strong>Total Amount from Completed Transactions:</strong> $total_should_be USD</p>";
        echo "<p><strong>Current Balance:</strong> {$user['balance']} USD</p>";
        
        $missing_amount = $total_should_be - $user['balance'];
        
        if ($missing_amount > 0) {
            echo "<p><strong>❌ Missing Amount:</strong> $missing_amount USD</p>";
            echo "<h3>Fixing Balance...</h3>";
            
            // Update balance to correct amount
            $stmt = $pdo->prepare("UPDATE telegram_users SET balance = ? WHERE id = ?");
            $result = $stmt->execute([$total_should_be, $user['id']]);
            
            if ($result) {
                echo "<p>✅ Balance updated to $total_should_be USD</p>";
                
                // Record the fix
                $stmt = $pdo->prepare("
                    INSERT INTO balance_transactions 
                    (telegram_user_id, transaction_type, amount, description) 
                    VALUES (?, 'adjustment', ?, 'Balance correction - Fixed missing amount')
                ");
                $stmt->execute([$user['id'], $missing_amount]);
                
                echo "<p>✅ Balance correction recorded</p>";
                echo "<h3>🎉 Balance Fixed! Test with /balance command in bot</h3>";
            } else {
                echo "<p>❌ Failed to update balance</p>";
            }
        } else {
            echo "<p>✅ Balance is correct!</p>";
        }
        
    } else {
        echo "<p>No completed transactions found</p>";
    }
    
    // Show current balance transactions
    echo "<h3>Current Balance Transactions:</h3>";
    $stmt = $pdo->prepare("SELECT * FROM balance_transactions WHERE telegram_user_id = ? ORDER BY created_at DESC LIMIT 10");
    $stmt->execute([$user['id']]);
    $balance_txs = $stmt->fetchAll();
    
    if ($balance_txs) {
        echo "<table border='1'>";
        echo "<tr><th>Type</th><th>Amount</th><th>Description</th><th>Date</th></tr>";
        foreach ($balance_txs as $tx) {
            echo "<tr>";
            echo "<td>{$tx['transaction_type']}</td>";
            echo "<td>{$tx['amount']}</td>";
            echo "<td>{$tx['description']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No balance transactions found</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
