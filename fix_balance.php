<?php
require_once 'config.php';

echo "<h2>Fix Balance for Completed Transactions</h2>";

try {
    $chat_id = 1630035459; // Your Telegram ID
    
    // Get user info
    $stmt = $pdo->prepare("SELECT id, balance FROM telegram_users WHERE telegram_id = ?");
    $stmt->execute([$chat_id]);
    $user = $stmt->fetch();
    
    if (!$user) {
        echo "<p>❌ User not found</p>";
        exit;
    }
    
    echo "<p><strong>Current Balance:</strong> {$user['balance']} USD</p>";
    
    // Find completed bot transactions that don't have corresponding balance transactions
    $stmt = $pdo->prepare("
        SELECT bt.* 
        FROM bot_transactions bt
        LEFT JOIN balance_transactions blt ON blt.description LIKE CONCAT('%', bt.md5, '%') AND blt.telegram_user_id = ?
        WHERE bt.chat_id = ? 
        AND bt.status = 'completed' 
        AND blt.id IS NULL
        ORDER BY bt.created_at DESC
    ");
    $stmt->execute([$user['id'], $chat_id]);
    $missing_balance_updates = $stmt->fetchAll();
    
    if ($missing_balance_updates) {
        echo "<h3>Found " . count($missing_balance_updates) . " completed transactions without balance updates:</h3>";
        
        foreach ($missing_balance_updates as $tx) {
            echo "<p>Processing MD5: {$tx['md5']}, Amount: {$tx['amount']}</p>";
            
            $pdo->beginTransaction();
            
            try {
                // Update balance
                $stmt = $pdo->prepare("UPDATE telegram_users SET balance = balance + ? WHERE id = ?");
                $stmt->execute([$tx['amount'], $user['id']]);
                
                // Record transaction
                $stmt = $pdo->prepare("
                    INSERT INTO balance_transactions 
                    (telegram_user_id, transaction_type, amount, description) 
                    VALUES (?, 'topup', ?, ?)
                ");
                $stmt->execute([$user['id'], $tx['amount'], "Top-up via KHQR API - MD5: {$tx['md5']} (Fixed)"]);
                
                $pdo->commit();
                echo "<p>✅ Fixed balance for MD5: {$tx['md5']}</p>";
                
            } catch (Exception $e) {
                $pdo->rollBack();
                echo "<p>❌ Error fixing MD5 {$tx['md5']}: " . $e->getMessage() . "</p>";
            }
        }
        
        // Get updated balance
        $stmt = $pdo->prepare("SELECT balance FROM telegram_users WHERE id = ?");
        $stmt->execute([$user['id']]);
        $new_balance = $stmt->fetchColumn();
        
        echo "<h3>✅ Balance Fixed!</h3>";
        echo "<p><strong>New Balance:</strong> $new_balance USD</p>";
        
    } else {
        echo "<p>✅ No missing balance updates found. All completed transactions have corresponding balance records.</p>";
    }
    
    // Show current balance transactions
    echo "<h3>Current Balance Transactions:</h3>";
    $stmt = $pdo->prepare("
        SELECT * FROM balance_transactions 
        WHERE telegram_user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ");
    $stmt->execute([$user['id']]);
    $transactions = $stmt->fetchAll();
    
    if ($transactions) {
        echo "<table border='1'>";
        echo "<tr><th>Amount</th><th>Description</th><th>Date</th></tr>";
        foreach ($transactions as $tx) {
            echo "<tr>";
            echo "<td>{$tx['amount']}</td>";
            echo "<td>{$tx['description']}</td>";
            echo "<td>{$tx['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
