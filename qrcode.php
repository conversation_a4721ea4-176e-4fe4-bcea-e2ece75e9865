<?php
// Connect to the database
include 'config.php';

// Enable error reporting
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);

// Establish database connection
try {
    $conn = mysqli_connect(DB_HOST, DB_USER, DB_PASS, DB_NAME);
    
    // Check connection
    if (!$conn) {
        throw new mysqli_sql_exception("Connection failed: " . mysqli_connect_error());
    }
} catch (mysqli_sql_exception $e) {
    // Log the connection error
    error_log("Database Connection Error: " . $e->getMessage());
    die("Connection failed: " . $e->getMessage());
}

// Get the parameters from the URL
$qrData = mysqli_real_escape_string($conn, $_GET['qr']);
$amount = mysqli_real_escape_string($conn, $_GET['amount']);
$md5 = mysqli_real_escape_string($conn, $_GET['md5']);
$username = mysqli_real_escape_string($conn, $_GET['username']);

// Note: We don't insert data here because save.php already does that before redirecting here
// This prevents duplicate entries in the database

// Close the database connection
mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en">

<head>
  <title>qrcode - KHQR</title>
  <meta property="og:title" content="qrcode - KHQR" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta charset="utf-8" />
  <meta property="twitter:card" content="summary_large_image" />

  <style data-tag="reset-style-sheet">
    html {
      line-height: 1.15;
    }

    body {
      margin: 0;
    }

    * {
      box-sizing: border-box;
      border-width: 0;
      border-style: solid;
    }

    p,
    li,
    ul,
    pre,
    div,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    figure,
    blockquote,
    figcaption {
      margin: 0;
      padding: 0;
    }

    button {
      background-color: transparent;
    }

    button,
    input,
    optgroup,
    select,
    textarea {
      font-family: inherit;
      font-size: 100%;
      line-height: 1.15;
      margin: 0;
    }

    button,
    select {
      text-transform: none;
    }

    button,
    [type="button"],
    [type="reset"],
    [type="submit"] {
      -webkit-appearance: button;
    }

    button::-moz-focus-inner,
    [type="button"]::-moz-focus-inner,
    [type="reset"]::-moz-focus-inner,
    [type="submit"]::-moz-focus-inner {
      border-style: none;
      padding: 0;
    }

    button:-moz-focus,
    [type="button"]:-moz-focus,
    [type="reset"]:-moz-focus,
    [type="submit"]:-moz-focus {
      outline: 1px dotted ButtonText;
    }

    a {
      color: inherit;
      text-decoration: inherit;
    }

    input {
      padding: 2px 4px;
    }

    img {
      display: block;
    }

    html {
      scroll-behavior: smooth
    }
  </style>
  <style data-tag="default-style-sheet">
    html {
      font-family: Inter;
      font-size: 16px;
    }

    body {
      font-weight: 400;
      font-style: normal;
      text-decoration: none;
      text-transform: none;
      letter-spacing: normal;
      line-height: 1.15;
      color: var(--dl-color-gray-black);
      background-color: var(--dl-color-gray-white);


    }

    .qrcode-loadingpic {
      /* Set initial rotation */
      transform: rotate(0deg);

      /* Define the animation */
      animation: rotateAnimation 1s linear infinite;
    }

    /* Define the keyframes for the rotation animation */
    @keyframes rotateAnimation {
      from {
        transform: rotate(0deg);
      }

      to {
        transform: rotate(360deg);
      }
    }
  </style>
  <link rel="shortcut icon" href="img/unnamed.png" type="icon/png" sizes="32x32" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Dangrek:wght@400&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet"
    href="https://fonts.googleapis.com/css2?family=Fira+Sans:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&amp;display=swap"
    data-tag="font" />
  <link rel="stylesheet" href="https://unpkg.com/@teleporthq/teleport-custom-scripts/dist/style.css" />
</head>

<body>
  <link rel="stylesheet" href="css/style.css" />
  <div>
    <link href="css/qrcode.css" rel="stylesheet" />

    <div class="qrcode-container">
      <div class="qrcode-body body">
        <img src="https://link.payway.com.kh/images/loading.svg" alt="image" class="qrcode-loadingpic" />
        <span class="qrcode-minutes">
          <span id="countdown">3:00</span>
          <br />
          <br />
        </span>
        <span id="name" class="qrcode-name"><b style="font-size:13px;">CamboKey - By CamboTeam</b></span>
        <span id="currency" class="qrcode-currency">$</span>
        <span id="amount" class="qrcode-amount">
          0.00
        </span>
        <div class="qrcode-head">
          <div class="qrcode-header">
            <div class="qrcode-container1">
              <div class="qrcode-container2"></div>
              <div class="qrcode-container3">
                <div class="qrcode-container4 qrhrader"></div>
                <img alt="image" src="img/khqr%20logo-200h.png" class="qrcode-image logo" />
              </div>
            </div>
          </div>
        </div>
        <div class="qrcode-line line">
          <div class="qrcode-qrcode qrcode">
           <img id="qr-image" alt="image" src="" class="qrcode-qr" />
            <img id="logo" alt="image" src="https://checkout.payway.com.kh/images/usd-khqr-logo.svg"
              class="qrcode-logo" /> 
          </div>
        </div>
        <img alt="image"
          src="img/payment_icons-cd5e952dde3b886dea1fd1b983d43ce372f1692dec253808ec654096d2feb701-200h.png"
          class="qrcode-banklogo" />
      </div>
    </div>
  </div>
</body>
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Function to check MD5 API
    async function checkMd5Api() {
      const url = 'https://api-bakong.nbc.gov.kh/v1/check_transaction_by_md5';
      const md5FromUrl = getParameterByName("md5");

      const data = {
        md5: md5FromUrl
      };

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.2CoTXA22afg-iveEdGkL8uusGjqIC64WXHum1Ae390k',
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        });

        if (response.ok) {
          const responseData = await response.json();

          if (responseData.responseCode === 0) {
            // Function to generate a unique transaction ID
            function generateTransactionId() {
                return Date.now().toString(36) + Math.random().toString(36).substr(2);
            }

            // Generate a unique transaction ID
            const transaction_id = generateTransactionId();

            // Get the username and amount from the URL parameters
            const usernameFromUrl = getParameterByName("username");
            const amountFromUrl = getParameterByName("amount");

            // Check if the transaction is valid (you can add more validation if needed)
            if (responseData.data.amount == amountFromUrl) {
                // Redirect to successful.php with amount, username, and transaction_id parameters
                window.location.href = `successful.php?amount=${amountFromUrl}&username=${usernameFromUrl}&transaction_id=${transaction_id}`;
                
                // Also send notification to Telegram bot
                fetch(`telegram_bot.php?telegram_notification=true&amount=${amountFromUrl}&username=${usernameFromUrl}&transaction_id=${transaction_id}`)
                    .then(response => response.json())
                    .catch(error => console.error('Telegram notification error:', error));
            } else {
                // Redirect to successful.php even if amount doesn't match exactly
                window.location.href = `successful.php?amount=${amountFromUrl}&username=${usernameFromUrl}&transaction_id=${transaction_id}`;
                
                // Also send notification to Telegram bot
                fetch(`telegram_bot.php?telegram_notification=true&amount=${amountFromUrl}&username=${usernameFromUrl}&transaction_id=${transaction_id}`)
                    .then(response => response.json())
                    .catch(error => console.error('Telegram notification error:', error));
            }
          } else {
            // Handle other response codes or display a message
            console.error('API response code is not 0:', responseData.responseCode);
            window.location.href = `successful.php?amount=${amountFromUrl}&username=${usernameFromUrl}`;
          }
        } else {
          console.error('Failed to fetch data:', response.status);
        }
      } catch (error) {
        console.error('Error during fetch:', error);
      }
    }

    // Call checkMd5Api every 5 seconds
    setInterval(checkMd5Api, 5000); // 5000 milliseconds = 5 seconds
  });

  // Function to get the value of a URL parameter
  function getParameterByName(name, url) {
    if (!url) url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    const regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
      results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return "";
    return decodeURIComponent(results[2].replace(/\+/g, " "));
  }

  // Get the "amount" and "qr" parameters from the URL
  const amountFromUrl = getParameterByName("amount");
  const qrFromUrl = getParameterByName("qr");

  // Set the obtained amount to an HTML element with ID "amount"
  const amountElement = document.getElementById("amount");
  if (amountElement) {
    amountElement.textContent = amountFromUrl || "Default Amount"; // Set a default value if amountFromUrl is null
  }

  // Set the obtained qr to an HTML element with ID "qr-code"
  const qrCodeElement = document.getElementById("qr-image");
  if (qrCodeElement) {
    qrCodeElement.textContent = qrFromUrl || "Default QR"; // Set a default value if qrFromUrl is null

    // Generate the QR code image and set it as the source for an <img> tag
    const qrImage = document.getElementById("qr-image");
    if (qrImage) {
      qrImage.src = `https://api.qrserver.com/v1/create-qr-code/?size=190x190&data=${qrFromUrl}`;
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Set the countdown duration in seconds
    let countdownDuration = 180; // 3 minutes * 60 seconds

    // Get the countdown element
    const countdownElement = document.getElementById("countdown");

    // Update the countdown every second
    const countdownInterval = setInterval(function () {
      const minutes = Math.floor(countdownDuration / 60);
      const seconds = countdownDuration % 60;

      // Display the countdown in the format MM:SS
      countdownElement.textContent = `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;

      // Decrease the countdown duration
      countdownDuration--;

      // Check if the countdown has reached 0
      if (countdownDuration < 0) {
        // Redirect to index.php when the countdown reaches 0
        window.location.href = 'expire.php';

        // Clear the interval to stop the countdown
        clearInterval(countdownInterval);
      }
    }, 1000); // Update every 1000 milliseconds (1 second)
  });
</script>

</html>